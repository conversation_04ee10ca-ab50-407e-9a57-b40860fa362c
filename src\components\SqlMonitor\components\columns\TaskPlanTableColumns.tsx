/**
 * 表格列配置组件
 * 用于定义任务管理表格的列结构、筛选器和操作按钮
 */

import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Button, Input, Popconfirm, Space, Tag } from 'antd';
import type { FilterDropdownProps, FilterValue } from 'antd/es/table/interface';
import React from 'react';

import type { TaskBasic } from '@/components/SqlMonitor/types';
import { TABLE_COLUMN_WIDTHS } from '@/components/SqlMonitor/constants';
import { tableStyles } from '@/components/SqlMonitor/styles';

/**
 * 表格列配置组件的属性接口
 */
interface TableColumnsProps {
  /** 表格筛选状态信息 */
  filteredInfo: Record<string, FilterValue | null>;
  /** 获取指定列的排序状态 */
  getSortOrder: (columnKey: string) => 'ascend' | 'descend' | null;
  /** 编辑任务的回调函数 */
  onEdit: (record: TaskBasic) => void;
  /** 删除任务的回调函数 */
  onDelete: (id: number) => void;
}

/**
 * 获取列搜索筛选器配置
 * 为指定列生成搜索筛选下拉框的配置对象
 *
 * @param dataIndex - 数据字段名
 * @param placeholder - 搜索框占位符文本
 * @param filteredInfo - 当前筛选状态信息
 * @returns 列筛选器配置对象
 */
const getColumnSearchProps = (dataIndex: string, placeholder: string, filteredInfo: Record<string, FilterValue | null>) => ({
  // 自定义筛选下拉框组件
  filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: FilterDropdownProps) => (
    <div style={{ padding: 8 }}>
      {/* 搜索输入框 */}
      <Input placeholder={`搜索 ${placeholder}`} value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])} onPressEnter={() => confirm()} style={{ marginBottom: 8, display: 'block' }} />
      {/* 操作按钮组 */}
      <Space>
        {/* 搜索按钮 */}
        <Button type="primary" onClick={() => confirm()} icon={<SearchOutlined />} size="small" style={{ width: 90 }}>
          搜索
        </Button>
        {/* 重置按钮 */}
        <Button
          onClick={() => {
            if (clearFilters) {
              clearFilters();
            }
            confirm();
          }}
          size="small"
          style={{ width: 90 }}
        >
          重置
        </Button>
      </Space>
    </div>
  ),
  // 筛选图标，根据是否有筛选条件显示不同颜色
  filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
  // 筛选逻辑：模糊匹配（不区分大小写）
  onFilter: (value: React.Key | boolean, record: TaskBasic) => record[dataIndex as keyof TaskBasic]?.toString().toLowerCase().includes(value.toString().toLowerCase()),
  // 当前筛选值
  filteredValue: filteredInfo[dataIndex] || null,
});

/**
 * 获取列选择筛选器配置
 * 为指定列生成下拉选择筛选器的配置对象
 *
 * @param dataIndex - 数据字段名
 * @param options - 筛选选项数组，包含显示文本和值
 * @param filteredInfo - 当前筛选状态信息
 * @returns 列筛选器配置对象
 */
const getColumnSelectProps = (dataIndex: string, options: { text: string; value: string }[], filteredInfo: Record<string, FilterValue | null>) => ({
  // 筛选选项
  filters: options,
  // 筛选逻辑
  onFilter: (value: React.Key | boolean, record: TaskBasic) => {
    const fieldValue = record[dataIndex as keyof TaskBasic];
    // 状态字段使用精确匹配
    if (dataIndex === 'status') {
      return fieldValue === value;
    }
    // 其他字段使用包含匹配
    return fieldValue?.toString().includes(value.toString());
  },
  // 当前筛选值
  filteredValue: filteredInfo[dataIndex] || null,
});

/**
 * 创建表格列配置
 * 根据传入的参数生成完整的表格列配置数组
 *
 * @param props - 表格列配置参数
 * @param props.filteredInfo - 筛选状态信息
 * @param props.getSortOrder - 获取排序状态的函数
 * @param props.onEdit - 编辑操作回调函数
 * @param props.onDelete - 删除操作回调函数
 * @returns 表格列配置数组
 */
export const createTaskPlanTableColumns = ({ filteredInfo, getSortOrder, onEdit, onDelete }: TableColumnsProps): TableColumnsType<TaskBasic> => [
  // 任务名称列
  {
    title: '任务名称',
    dataIndex: 'name',
    key: 'name',
    width: TABLE_COLUMN_WIDTHS.name,
    ellipsis: true, // 超长文本显示省略号
    fixed: 'left', // 固定在左侧
    sorter: (a, b) => a.name.localeCompare(b.name), // 中文排序
    sortOrder: getSortOrder('name'),
    ...getColumnSearchProps('name', '任务名称', filteredInfo),
  },
  // 任务分组列
  {
    title: '任务分组',
    dataIndex: 'group_name',
    key: 'group_name',
    width: TABLE_COLUMN_WIDTHS.group_name,
    sorter: (a, b) => a.group_name.localeCompare(b.group_name), // 中文排序
    sortOrder: getSortOrder('group_name'),
    ...getColumnSearchProps('group_name', '任务分组', filteredInfo),
  },
  // 执行时间列（组合显示开始时间和结束时间）
  {
    title: '执行时间',
    key: 'time',
    width: TABLE_COLUMN_WIDTHS.time,
    sorter: (a, b) => a.start_time.localeCompare(b.start_time), // 按开始时间排序
    sortOrder: getSortOrder('start_time'),
    filteredValue: null, // 不支持筛选
    render: (_, record) => (
      <span>
        {record.start_time} - {record.end_time}
      </span>
    ),
  },
  // 星期列（支持数组和字符串格式）
  {
    title: '星期',
    dataIndex: 'weekday',
    key: 'weekday',
    width: TABLE_COLUMN_WIDTHS.weekday,
    sorter: (a, b) => {
      // 处理数组格式的星期数据
      const aWeekday = Array.isArray(a.weekday) ? a.weekday.join(',') : a.weekday;
      const bWeekday = Array.isArray(b.weekday) ? b.weekday.join(',') : b.weekday;
      return aWeekday.localeCompare(bWeekday);
    },
    sortOrder: getSortOrder('weekday'),
    render: (weekday: string | string[]) => {
      // 渲染星期信息，数组格式用逗号连接
      if (Array.isArray(weekday)) {
        return weekday.join(',');
      }
      return weekday;
    },
  },
  // 执行频率列（支持对象和字符串格式）
  {
    title: '执行频率',
    dataIndex: 'frequency',
    key: 'frequency',
    width: TABLE_COLUMN_WIDTHS.frequency,
    sorter: (a, b) => {
      // 处理对象格式的频率数据
      const aFreq = typeof a.frequency === 'object' ? `${a.frequency.value}${a.frequency.unit}` : a.frequency;
      const bFreq = typeof b.frequency === 'object' ? `${b.frequency.value}${b.frequency.unit}` : b.frequency;
      return aFreq.toString().localeCompare(bFreq.toString());
    },
    sortOrder: getSortOrder('frequency'),
    filteredValue: null, // 不支持筛选
    render: (frequency: string | { value: number; unit: string }) => {
      // 渲染频率信息，对象格式组合显示
      if (frequency && typeof frequency === 'object') {
        return `${frequency.value}${frequency.unit}`;
      }
      return frequency;
    },
  },
  // 状态列（带筛选和标签显示）
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: TABLE_COLUMN_WIDTHS.status,
    sorter: (a, b) => a.status.localeCompare(b.status), // 状态排序
    sortOrder: getSortOrder('status'),
    // 添加状态筛选器
    ...getColumnSelectProps(
      'status',
      [
        { text: '启用', value: 'enabled' },
        { text: '禁用', value: 'disabled' },
      ],
      filteredInfo
    ),
    // 渲染状态标签，不同状态显示不同颜色
    render: (status: string) => <Tag color={status === 'enabled' ? 'success' : 'error'}>{status === 'enabled' ? '启用' : '禁用'}</Tag>,
  },
  // 重试次数列
  {
    title: '重试次数',
    dataIndex: 'retry_num',
    key: 'retry_num',
    width: TABLE_COLUMN_WIDTHS.retry_num,
    sorter: (a, b) => parseInt(a.retry_num) - parseInt(b.retry_num), // 数值排序
    sortOrder: getSortOrder('retry_num'),
  },
  // 重试间隔列（支持对象和字符串格式）
  {
    title: '重试间隔',
    dataIndex: 'retry_frequency',
    key: 'retry_frequency',
    width: TABLE_COLUMN_WIDTHS.retry_frequency,
    sorter: (a, b) => {
      // 处理对象格式的重试间隔数据
      const aRetryFreq = typeof a.retry_frequency === 'object' ? `${a.retry_frequency.value}${a.retry_frequency.unit}` : a.retry_frequency;
      const bRetryFreq = typeof b.retry_frequency === 'object' ? `${b.retry_frequency.value}${b.retry_frequency.unit}` : b.retry_frequency;
      return aRetryFreq.toString().localeCompare(bRetryFreq.toString());
    },
    sortOrder: getSortOrder('retry_frequency'),
    filteredValue: null, // 不支持筛选
    render: (retry_frequency: string | { value: number; unit: string }) => {
      // 渲染重试间隔信息，对象格式组合显示
      if (retry_frequency && typeof retry_frequency === 'object') {
        return `${retry_frequency.value}${retry_frequency.unit}`;
      }
      return retry_frequency;
    },
  },
  // 操作列（编辑和删除按钮）
  {
    title: '操作',
    key: 'action',
    width: TABLE_COLUMN_WIDTHS.action,
    fixed: 'right', // 固定在右侧
    filteredValue: null, // 不支持筛选
    render: (_, record) => (
      <Space size="small">
        {/* 编辑按钮 */}
        <Button type="text" size="small" icon={<EditOutlined />} onClick={() => onEdit(record)} className={tableStyles.editButton}>
          编辑
        </Button>
        {/* 删除按钮（带确认弹窗） */}
        <Popconfirm title="确认删除" description="确定要删除这个任务吗？" onConfirm={() => onDelete(record.id)} okText="确定" cancelText="取消" placement="topRight">
          <Button type="text" size="small" danger icon={<DeleteOutlined />} className={tableStyles.deleteButton}>
            删除
          </Button>
        </Popconfirm>
      </Space>
    ),
  },
];
