import { useState, useEffect } from 'react';
import { message } from 'antd';

// 导入重构后的模块
import type { TaskAlert, DBConnection, AlertSend, OtherInfo } from '../types';
import { TaskService } from '../services';
import { MonitorItemService } from '../services/monitorItem';

interface UseAvailableDataReturn {
  availableAlerts: TaskAlert[];
  availableDbConnections: DBConnection[];
  availableAlertSends: AlertSend[];
  availableOtherInfos: OtherInfo[];
  loadAvailableData: () => Promise<void>;
  refreshAvailableData: () => Promise<void>;
}

/**
 * 可选数据管理 Hook
 * 管理所有可选择的数据列表
 */
export const useAvailableData = (): UseAvailableDataReturn => {
  // 可选择的数据
  const [availableAlerts, setAvailableAlerts] = useState<TaskAlert[]>([]);
  const [availableDbConnections, setAvailableDbConnections] = useState<DBConnection[]>([]);
  const [availableAlertSends, setAvailableAlertSends] = useState<AlertSend[]>([]);
  const [availableOtherInfos, setAvailableOtherInfos] = useState<OtherInfo[]>([]);

  // 加载可选数据
  const loadAvailableData = async () => {
    try {
      const [alertsRes, dbConnectionsRes, alertSendsRes, otherInfosRes] = await Promise.all([
        // TaskService.getAlerts(),
        MonitorItemService.getAlerts({}),
        TaskService.getDbConnections({}),
        TaskService.getAlertSends({}),
        TaskService.getOtherInfos(),
      ]);

      setAvailableAlerts(alertsRes);
      setAvailableDbConnections(dbConnectionsRes);
      setAvailableAlertSends(alertSendsRes);
      setAvailableOtherInfos(otherInfosRes);
    } catch (error) {
      console.error('加载可选数据失败:', error);
      message.error('加载数据失败');
    }
  };

  // 刷新可选数据（用于新增数据后更新列表）
  const refreshAvailableData = async () => {
    await loadAvailableData();
  };

  // 初始化时加载数据
  useEffect(() => {
    loadAvailableData();
  }, []);

  return {
    availableAlerts,
    availableDbConnections,
    availableAlertSends,
    availableOtherInfos,
    loadAvailableData,
    refreshAvailableData,
  };
};
