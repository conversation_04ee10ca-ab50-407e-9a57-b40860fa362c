import { httpClient } from '@/services/http/client';
import type { ApiResponse, DBConnection, DBConnectionSearchParams } from '../types';

export class DBConnectionService {
  static async getDbConnections(params: Partial<DBConnectionSearchParams>): Promise<ApiResponse<DBConnection>> {
    const res = httpClient.post<ApiResponse<DBConnection>>('/api/v1/db/connection/select', params);
    return res;
  }

  // static async getDbConnectionBy(params: Partial<DBConnectionSearchParams>): Promise<ApiResponse<DBConnection>> {
  //   const res = httpClient.post<ApiResponse<DBConnection>>('/api/v1/db/connection/select', params);
  //   return res;
  // }

  static async deleteDbConnection(id: number): Promise<ApiResponse<DBConnection>> {
    console.log('删除数据库连接:', id);
    const params = {
      ids: [id],
    };

    const response = await httpClient.post<ApiResponse<DBConnection>>('/api/v1/db/connection/delete/id', params);
    console.log('删除数据库连接响应:', response);
    return response;
  }
  static async batchDeleteDbConnections(ids: number[]): Promise<ApiResponse<DBConnection>> {
    console.log('批量删除数据库连接:', ids);

    const params = {
      ids: ids,
    };

    const response = await httpClient.post<ApiResponse<DBConnection>>('/api/v1/db/connection/delete/id', params);
    console.log('删除数据库连接响应:', response);
    return response;
  }
  static async addDbConnection(params: DBConnection): Promise<ApiResponse<DBConnection>> {
    console.log('更新数据库连接:', params);

    const res = httpClient.post<ApiResponse<DBConnection>>('/api/v1/db/connection/add', params);

    return res;
  }
  static async updateDbConnection(params: DBConnection): Promise<ApiResponse<DBConnection>> {
    console.log('更新数据库连接:', params);

    const res = httpClient.post<ApiResponse<DBConnection>>('/api/v1/db/connection/update', params);

    return res;
  }
}
