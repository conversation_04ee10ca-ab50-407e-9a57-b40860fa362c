/* 选择告警Modal表格样式优化 */

/* 表格行样式 */
.table-row-light {
  background-color: #fafafa;
}

.table-row-dark {
  background-color: #ffffff;
}

/* 表格行悬停效果 */
.table-row-light:hover,
.table-row-dark:hover {
  background-color: #e6f7ff !important;
  transition: background-color 0.2s ease;
}

/* 表格头部样式 */
.ant-table-thead > tr > th {
  background-color: #f0f2f5;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #d9d9d9;
}

/* 选中行样式 */
.ant-table-tbody > tr.ant-table-row-selected > td {
  background-color: #bae7ff !important;
}

.ant-table-tbody > tr.ant-table-row-selected:hover > td {
  background-color: #91d5ff !important;
}

/* 表格边框优化 */
.ant-table-bordered .ant-table-tbody > tr > td {
  border-right: 1px solid #f0f0f0;
}

/* 分页器样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

.ant-pagination .ant-pagination-item {
  border-radius: 4px;
}

.ant-pagination .ant-pagination-item-active {
  background-color: #1890ff;
  border-color: #1890ff;
}

/* 搜索框样式 */
.search-input {
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-input:focus,
.search-input:hover {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式优化 */
.search-button {
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.reset-button {
  border-radius: 6px;
  border-color: #d9d9d9;
}

.reset-button:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

/* 标签样式优化 */
.severity-tag {
  border-radius: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

.type-tag {
  border-radius: 12px;
  font-weight: 500;
  padding: 2px 8px;
}

/* SQL代码块样式 */
.sql-code {
  background-color: #f6f8fa;
  border: 1px solid #e1e4e8;
  border-radius: 4px;
  padding: 4px 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  color: #24292e;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 时间显示样式 */
.time-text {
  color: #8c8c8c;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* 告警名称样式 */
.alert-name {
  font-weight: 600;
  color: #1890ff;
  cursor: pointer;
}

.alert-name:hover {
  color: #40a9ff;
  text-decoration: underline;
}

/* 表格容器样式 */
.table-container {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 提示信息样式 */
.info-banner {
  background: linear-gradient(90deg, #e6f7ff 0%, #bae7ff 100%);
  border: 1px solid #91d5ff;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 16px;
}

.info-banner .info-text {
  color: #0050b3;
  font-size: 13px;
}

/* 自定义Modal样式 */
.custom-modal .ant-modal-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  border-radius: 8px 8px 0 0;
  padding: 20px 24px;
}

.custom-modal .ant-modal-body {
  padding: 24px;
}

.custom-modal .ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 自定义Drawer样式 */
.custom-drawer .ant-drawer-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  padding: 20px 24px;
}

.custom-drawer .ant-drawer-body {
  padding: 24px;
}

.custom-drawer .ant-drawer-footer {
  padding: 16px 24px;
  background-color: #f8fafc;
}

/* 滚动条样式 */
.ant-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载状态样式 */
.ant-spin-container {
  border-radius: 8px;
}

/* 空数据样式 */
.ant-empty {
  padding: 40px 0;
}

.ant-empty-description {
  color: #8c8c8c;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .ant-modal {
    width: 95% !important;
    max-width: 1000px;
  }
}

/* 响应式优化 */
@media (max-width: 768px) {
  .custom-modal .ant-modal-body,
  .custom-drawer .ant-drawer-body {
    padding: 16px;
  }
}
