import React from 'react';
import { Modal } from 'antd';

/**
 * 确认Modal组件Props
 */
interface ConfirmModalProps {
  visible: boolean;
  title: string;
  content: React.ReactNode;
  onConfirm: () => void;
  onCancel: () => void;
  okText?: string;
  cancelText?: string;
  okType?: 'primary' | 'danger';
  loading?: boolean;
  width?: number;
}

/**
 * 通用确认Modal组件
 * 提供统一的确认对话框样式和行为
 */
export const ConfirmModal: React.FC<ConfirmModalProps> = ({ visible, title, content, onConfirm, onCancel, okText = '确定', cancelText = '取消', okType = 'primary', loading = false, width = 400 }) => {
  return (
    <Modal title={title} open={visible} onOk={onConfirm} onCancel={onCancel} okText={okText} cancelText={cancelText} okType={okType} confirmLoading={loading} width={width} destroyOnHidden>
      {typeof content === 'string' ? <p>{content}</p> : content}
    </Modal>
  );
};
