/**
 * 表单相关常量配置
 */

/**
 * 表单布局配置
 */
export const FORM_LAYOUT = {
  labelCol: { span: 6 },
  wrapperCol: { span: 18 },
} as const;

/**
 * 表单验证规则
 */
export const FORM_RULES = {
  required: { required: true, message: '此字段为必填项' },
  name: { required: true, message: '请输入任务名称' },
  group: { required: true, message: '请选择任务分组' },
  startTime: { required: true, message: '请选择开始时间' },
  endTime: { required: true, message: '请选择结束时间' },
  weekday: { required: true, message: '请选择执行星期' },
  frequency: { required: true, message: '请设置执行频率' },
} as const;

/**
 * 表单字段占位符
 */
export const FORM_PLACEHOLDERS = {
  name: '请输入任务名称',
  group: '请选择任务分组',
  status: '请选择任务状态',
  startTime: 'HH:mm:ss',
  endTime: 'HH:mm:ss',
  weekday: '请选择星期',
  frequency: '请选择执行频率',
  retryNum: '请输入重试次数',
  retryFrequency: '请设置重试间隔',
  alertReceiver: '请输入告警接收人',
  sql: '请输入SQL语句',
  host: '请输入主机地址',
  port: '请输入端口号',
  username: '请输入用户名',
  password: '请输入密码',
  database: '请输入数据库名',
  business: '请输入业务系统名称',
  businessEn: '请输入业务系统英文名称',
  hostname: '请输入主机名称',
  location: '请输入告警来源',
} as const;

/**
 * 表单按钮文本
 */
export const FORM_BUTTON_TEXT = {
  submit: '提交',
  cancel: '取消',
  reset: '重置',
  search: '搜索',
  add: '新增',
  edit: '编辑',
  delete: '删除',
  confirm: '确认',
  save: '保存',
  update: '更新',
} as const;
