import DBConnectionTable from '@/components/SqlMonitor/components/DBConnectionTable';
import React from 'react';
import { useOutletContext } from 'react-router-dom';

interface OutletContext {
  contentHeight: number;
}

/**
 * 数据库连接管理页面
 * 展示完整的数据库连接管理表格功能
 */
const DBConnectionTablePage: React.FC = () => {
  const { contentHeight } = useOutletContext<OutletContext>();
  return <DBConnectionTable contentHeight={contentHeight} />;
};

export default DBConnectionTablePage;
