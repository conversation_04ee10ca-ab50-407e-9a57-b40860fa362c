# 修复跨页选择失效问题

## 问题描述

在之前修复选择状态同步问题时，意外破坏了跨页选择功能。具体表现为：

1. 用户在第一页选择了一些数据
2. 切换到第二页选择更多数据
3. 再切换回第一页时，之前的选择状态丢失
4. 跨页选择的总数统计不正确

## 问题原因分析

### 原始问题代码
```typescript
// 之前的错误修复 - 完全重置allSelectedRows
if (crossPage) {
  // 跨页面模式：重新设置全局选择状态
  const newMap = new Map<React.Key, T>();
  selectedRowsFromKeys.forEach(row => {
    newMap.set(row.id, row);
  });
  setAllSelectedRows(newMap);  // ❌ 这里完全重置了Map，丢失了其他页面的选择
}
```

### 问题分析
- **状态覆盖**: 每次`initialSelectedKeys`变化时，完全重置`allSelectedRows`
- **丢失其他页面数据**: 新的Map只包含当前页面的数据，其他页面的选择被清除
- **破坏跨页逻辑**: 违背了跨页选择的核心原理

## 解决方案

### 修复后的正确逻辑
```typescript
// 修复后的代码 - 保持其他页面的选择状态
if (crossPage) {
  // 跨页面模式：只更新当前页面的数据到全局选择状态，不清除其他页面的选择
  setAllSelectedRows(prev => {
    const newMap = new Map(prev);  // ✅ 保留之前的状态
    
    // 先移除当前页面的所有数据
    data.forEach(item => {
      newMap.delete(item.id);
    });
    
    // 再添加应该选中的数据
    selectedRowsFromKeys.forEach(row => {
      newMap.set(row.id, row);
    });
    
    return newMap;  // ✅ 返回更新后的Map，保留其他页面的选择
  });
}
```

### 关键改进点

1. **保留现有状态**: 使用`new Map(prev)`保留其他页面的选择
2. **精确更新**: 只移除和更新当前页面的数据
3. **状态隔离**: 不同页面的选择状态相互独立

## 技术实现细节

### 1. 状态更新策略
```typescript
setAllSelectedRows(prev => {
  const newMap = new Map(prev);  // 复制现有状态
  
  // 清理当前页面的旧状态
  data.forEach(item => {
    newMap.delete(item.id);
  });
  
  // 设置当前页面的新状态
  selectedRowsFromKeys.forEach(row => {
    newMap.set(row.id, row);
  });
  
  return newMap;
});
```

### 2. 清空选择的处理
```typescript
} else if (initialSelectedKeys.length === 0) {
  // 如果initialSelectedKeys为空，只清空当前页面的选择状态
  if (crossPage) {
    setAllSelectedRows(prev => {
      const newMap = new Map(prev);
      // 只移除当前页面的数据，保留其他页面的选择
      data.forEach(item => {
        newMap.delete(item.id);
      });
      return newMap;
    });
  }
  // ...
}
```

### 3. 跨页选择的工作原理

#### 数据结构
- `allSelectedRows`: Map<React.Key, T> - 存储所有页面的选择状态
- `selection`: 当前页面的选择状态
- `data`: 当前页面的数据

#### 状态同步流程
```
用户在页面A选择数据
  ↓
更新allSelectedRows (包含页面A的选择)
  ↓
用户切换到页面B
  ↓
根据allSelectedRows更新页面B的显示状态
  ↓
用户在页面B选择数据
  ↓
更新allSelectedRows (保留页面A + 添加页面B)
  ↓
用户切换回页面A
  ↓
根据allSelectedRows恢复页面A的选择状态
```

## 修复效果验证

### 1. 跨页选择恢复
- ✅ 在不同页面间切换时，选择状态正确保持
- ✅ 总选择数量统计正确
- ✅ 批量操作能获取到所有页面的选择数据

### 2. 状态同步正常
- ✅ 清空选择后再次选择发送，状态正确同步
- ✅ 不影响单页面选择模式
- ✅ 保持所有原有功能

### 3. 边界情况处理
- ✅ 空数据页面的处理
- ✅ 全选/取消全选的处理
- ✅ 页面数据变化时的状态更新

## 测试场景

### 1. 基本跨页选择测试
1. 在第1页选择2个项目
2. 切换到第2页选择3个项目
3. 切换回第1页，验证之前的2个项目仍然选中
4. 检查总选择数量是否为5

### 2. 清空选择测试
1. 跨页选择一些数据
2. 点击"取消全选"
3. 验证所有页面的选择都被清空

### 3. 选择发送功能测试
1. 清空所有选择
2. 点击"选择发送"
3. 验证预选中的数据正确显示
4. 进行跨页选择操作
5. 确认选择状态正确维护

## 总结

通过修改状态更新逻辑，从完全重置改为增量更新，成功修复了跨页选择失效的问题：

- **保持功能完整性**: 跨页选择功能完全恢复
- **状态管理优化**: 更精确的状态更新策略
- **向后兼容**: 不影响现有的所有功能
- **性能提升**: 避免不必要的状态重置

修复后，用户可以正常进行跨页选择操作，同时保持选择发送功能的状态同步能力。
