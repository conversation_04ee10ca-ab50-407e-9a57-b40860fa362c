import React, { useState, useCallback, useRef, useEffect } from 'react';
import { ListTable } from '@visactor/react-vtable';
import { Button, Space } from 'antd';

interface TestData {
  id: number;
  name: string;
  selected?: boolean;
}

const VTableTest: React.FC = () => {
  const [tableData, setTableData] = useState<TestData[]>([
    {
      id: 1,
      name: '张三',
      selected: false,
    },
    {
      id: 2,
      name: '李四',
      selected: false,
    },
    {
      id: 3,
      name: '王五',
      selected: false,
    },
    {
      id: 4,
      name: '赵六',
      selected: false,
    },
    {
      id: 5,
      name: '钱七',
      selected: false,
    },
  ]);

  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [indeterminate, setIndeterminate] = useState(false);
  const tableRef = useRef<typeof ListTable | null>(null);

  // 计算indeterminate状态
  useEffect(() => {
    const totalCount = tableData.length;
    const selectedCount = selectedRowKeys.length;
    const newIndeterminate = selectedCount > 0 && selectedCount < totalCount;
    setIndeterminate(newIndeterminate);

    console.log('测试页面状态更新:', {
      totalCount,
      selectedCount,
      indeterminate: newIndeterminate,
    });
  }, [selectedRowKeys.length, tableData.length]);

  // 处理行选择
  const handleRowSelect = useCallback((rowId: number, checked: boolean) => {
    setTableData(prevData =>
      prevData.map(item =>
        item.id === rowId
          ? {
              ...item,
              selected: checked,
            }
          : item
      )
    );

    setSelectedRowKeys(prevKeys => {
      if (checked) {
        return prevKeys.includes(rowId) ? prevKeys : [...prevKeys, rowId];
      } else {
        return prevKeys.filter(key => key !== rowId);
      }
    });
  }, []);

  // 全选/取消全选
  const handleSelectAll = useCallback((checked: boolean) => {
    setTableData(prevData => {
      const updatedData = prevData.map(item => ({
        ...item,
        selected: checked,
      }));

      if (checked) {
        setSelectedRowKeys(updatedData.map(item => item.id));
      } else {
        setSelectedRowKeys([]);
      }

      return updatedData;
    });
  }, []);

  const columns = [
    {
      field: 'selected',
      cellType: 'checkbox' as const,
      headerType: 'checkbox' as const,
      width: 80,
      checked: selectedRowKeys.length === tableData.length && tableData.length > 0,
      // 尝试在列级别设置indeterminate
      indeterminate: indeterminate,
      headerStyle: {
        textAlign: 'center' as const,
        // 同时在headerStyle中也设置
        indeterminate: indeterminate,
      },
      style: {
        textAlign: 'center' as const,
      },
    },
    {
      field: 'id',
      title: 'ID',
      width: 80,
    },
    {
      field: 'name',
      title: '姓名',
      width: 150,
    },
  ];

  return (
    <div style={{ padding: '20px' }}>
      <h2>VTable Indeterminate 测试</h2>

      <div style={{ marginBottom: '20px' }}>
        <p>
          选中数量: {selectedRowKeys.length} / {tableData.length}
        </p>
        <p>Indeterminate 状态: {indeterminate ? '是' : '否'}</p>
        <p>全选状态: {selectedRowKeys.length === tableData.length && tableData.length > 0 ? '是' : '否'}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <Space>
          <Button onClick={() => handleSelectAll(true)}>全选</Button>
          <Button onClick={() => handleSelectAll(false)}>取消全选</Button>
          <Button onClick={() => handleRowSelect(1, !tableData[0].selected)}>切换第一行</Button>
        </Space>
      </div>

      <div
        style={{
          width: '400px',
          height: '300px',
        }}
      >
        <ListTable
          ref={tableRef}
          records={tableData}
          columns={columns}
          width={400}
          height={300}
          onCheckboxStateChange={(args: { row: number; col: number; checked: boolean }) => {
            console.log('Checkbox状态变化:', args);
            const { row, checked } = args;
            const dataRowIndex = row - 1;

            if (dataRowIndex >= 0) {
              const record = tableData[dataRowIndex];
              if (record) {
                handleRowSelect(record.id, checked);
              }
            } else if (row === 0) {
              // 表头checkbox点击
              const shouldSelectAll = !(selectedRowKeys.length === tableData.length);
              handleSelectAll(shouldSelectAll);
            }
          }}
        />
      </div>
    </div>
  );
};

export default VTableTest;
