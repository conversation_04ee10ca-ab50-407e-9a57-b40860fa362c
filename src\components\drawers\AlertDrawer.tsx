import React, { useEffect } from 'react';
import { Drawer, Form, Input, Select, Row, Col, Button, Table, Tag } from 'antd';
import type { ColumnsType } from 'antd/es/table';

// 导入类型定义
import type { TaskAlert } from '../../types/task';
import { ALERT_TYPE_OPTIONS, ALERT_SEVERITY_OPTIONS } from '../../types/task';

const { Option } = Select;
const { TextArea } = Input;

interface AlertDrawerProps {
  visible: boolean;
  editingData?: TaskAlert;
  onCancel: () => void;
  onSubmit: (data: TaskAlert) => void;
}

// 告警抽屉组件
export const AlertDrawer: React.FC<AlertDrawerProps> = ({ visible, editingData, onCancel, onSubmit }) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible && editingData) {
      form.setFieldsValue(editingData);
    } else if (visible) {
      form.resetFields();
    }
  }, [visible, editingData, form]);

  const handleSubmit = async (values: any) => {
    const alertData: TaskAlert = {
      id: editingData?.id || Date.now(),
      name: values.name || '',
      severity: values.severity || 'low',
      sql: values.sql || '',
      type: values.type || 'isEqual',
      values: values.type === 'isEqual' ? values.values || [] : [],
      create_time: editingData?.create_time || new Date().toISOString(),
      update_time: new Date().toISOString(),
    };
    onSubmit(alertData);
  };

  // 表格列定义 - 参考分组管理表格的布局
  const columns: ColumnsType<TaskAlert> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 50,
    },
    {
      title: '告警名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: '告警级别',
      dataIndex: 'severity',
      key: 'severity',
      width: 100,
      render: (severity: string) => {
        const severityConfig = {
          low: { color: 'green', text: '低' },
          medium: { color: 'orange', text: '中' },
          high: { color: 'red', text: '高' },
          critical: { color: 'purple', text: '严重' },
        };
        const config = severityConfig[severity as keyof typeof severityConfig] || {
          color: 'default',
          text: severity,
        };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '告警类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => {
        const typeConfig = {
          isEqual: '等于',
          notEqual: '不等于',
          greaterThan: '大于',
          lessThan: '小于',
          contains: '包含',
          notContains: '不包含',
        };
        return typeConfig[type as keyof typeof typeConfig] || type;
      },
    },
    // {
    //   title: '创建时间',
    //   dataIndex: 'create_time',
    //   key: 'create_time',
    //   width: 180,
    //   ellipsis: true,
    //   sorter: (a, b) => a.create_time.localeCompare(b.create_time),
    //   render: (time: string) => new Date(time).toLocaleString(),
    // },
  ];

  // 示例数据 - 实际使用时应该从props或API获取
  const tableData = editingData ? [editingData] : [];

  return (
    <Drawer
      title={editingData ? '编辑告警' : '新增告警'}
      width="60%"
      open={visible}
      onClose={onCancel}
      className="custom-drawer"
      styles={{
        body: { paddingBottom: 80 },
      }}
      footer={
        <div className="flex justify-end gap-3">
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" onClick={() => form.submit()}>
            {editingData ? '更新' : '创建'}
          </Button>
        </div>
      }
    >
      <div className="space-y-6">
        {/* 表单区域 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium mb-4">告警配置</h3>
          <Form form={form} layout="vertical" onFinish={handleSubmit}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="告警名称" name="name" rules={[{ required: true, message: '请输入告警名称' }]}>
                  <Input placeholder="请输入告警名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="告警级别" name="severity" rules={[{ required: true, message: '请选择告警级别' }]}>
                  <Select placeholder="请选择告警级别">
                    {ALERT_SEVERITY_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="SQL语句" name="sql" rules={[{ required: true, message: '请输入SQL语句' }]}>
                  <TextArea rows={4} placeholder="请输入SQL语句" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="告警类型" name="type" rules={[{ required: true, message: '请选择告警类型' }]}>
                  <Select placeholder="请选择告警类型">
                    {ALERT_TYPE_OPTIONS.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="触发值" name="values" dependencies={['type']}>
                  {({ getFieldValue }) => <Select mode="tags" placeholder="请输入触发值" disabled={getFieldValue('type') !== 'isEqual'} />}
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>

        {/* 表格区域 - 参考分组管理表格的布局 */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-medium">告警预览</h3>
          </div>
          <div className="p-4">
            <Table columns={columns} dataSource={tableData} rowKey="id" size="small" className="rounded-lg overflow-hidden" scroll={{ y: 300 }} pagination={false} />
            {tableData.length === 0 && (
              <div
                style={{
                  textAlign: 'center',
                  padding: '40px 0',
                  color: '#999',
                }}
              >
                暂无告警数据，请填写表单后预览
              </div>
            )}
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default AlertDrawer;
