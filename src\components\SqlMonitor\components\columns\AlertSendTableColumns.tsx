/**
 * 告警发送表格列配置组件
 * 定义告警发送表格的列结构和操作
 */

import { DeleteOutlined, EditOutlined, SearchOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Button, Input, Popconfirm, Space, Tag } from 'antd';
import type { FilterDropdownProps, FilterValue } from 'antd/es/table/interface';

import type { AlertSend } from '@/components/SqlMonitor/types';
import { tableStyles } from '@/components/SqlMonitor/styles';

/**
 * 告警发送表格列配置组件的属性接口
 */
interface AlertSendTableColumnsProps {
  /** 表格筛选状态信息 */
  filteredInfo: Record<string, FilterValue | null>;
  /** 获取指定列的排序状态 */
  getSortOrder: (columnKey: string) => 'ascend' | 'descend' | null;
  /** 编辑告警发送的回调函数 */
  onEdit: (record: AlertSend) => void;
  /** 删除告警发送的回调函数 */
  onDelete: (id: number) => void;
}

/**
 * 创建告警发送表格列配置
 */
export const createAlertSendTableColumns = ({ filteredInfo, getSortOrder, onEdit, onDelete }: AlertSendTableColumnsProps): TableColumnsType<AlertSend> => {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      sorter: true,
      sortOrder: getSortOrder('id'),
      fixed: 'left',
    },
    {
      title: '发送名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      sorter: true,
      sortOrder: getSortOrder('name'),
      fixed: 'left',
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: FilterDropdownProps) => (
        <div className="p-2">
          <Input placeholder="搜索发送名称" value={selectedKeys[0]} onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])} onPressEnter={() => confirm()} className="w-48 mb-2 block" />
          <Space>
            <Button type="primary" onClick={() => confirm()} icon={<SearchOutlined />} size="small" className="w-20">
              搜索
            </Button>
            <Button onClick={() => clearFilters && clearFilters()} size="small" className="w-20">
              重置
            </Button>
          </Space>
        </div>
      ),
      filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
      filteredValue: filteredInfo.name || null,
    },
    {
      title: '接收类型',
      dataIndex: 'receive_type',
      key: 'receive_type',
      width: 120,
      sorter: true,
      sortOrder: getSortOrder('receive_type'),
      render: (type: string) => {
        const color = type === 'kafka' ? 'blue' : type === 'prometheus' ? 'green' : 'default';
        return <Tag color={color}>{type}</Tag>;
      },
      filters: [
        { text: 'Kafka', value: 'kafka' },
        { text: 'Prometheus', value: 'prometheus' },
      ],
      filteredValue: filteredInfo.type || null,
    },
    // {
    //   title: 'Kafka地址',
    //   dataIndex: ['kafka_receiver', 'address'],
    //   key: 'kafka_address',
    //   width: 200,
    //   render: (address: string, record: AlertSend) => {
    //     if (record.type === 'kafka' && record.kafka_receiver?.address) {
    //       return <span className="text-xs">{record.kafka_receiver.address}</span>;
    //     }
    //     return '-';
    //   },
    // },
    // {
    //   title: 'Kafka Topic',
    //   dataIndex: ['kafka_receiver', 'topic'],
    //   key: 'kafka_topic',
    //   width: 150,
    //   render: (topic: string, record: AlertSend) => {
    //     if (record.type === 'kafka' && record.kafka_receiver?.topic) {
    //       return <Tag color="orange">{record.kafka_receiver.topic}</Tag>;
    //     }
    //     return '-';
    //   },
    // },
    // {
    //   title: 'Prometheus地址',
    //   dataIndex: ['prometheus_receiver', 'address'],
    //   key: 'prometheus_address',
    //   width: 200,
    //   render: (address: string, record: AlertSend) => {
    //     if (record.type === 'prometheus' && record.prometheus_receiver?.address) {
    //       return <span className="text-xs">{record.prometheus_receiver.address}</span>;
    //     }
    //     return '-';
    //   },
    // },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 180,
      sorter: true,
      sortOrder: getSortOrder('create_time'),
      render: (time: string) => <span className="text-xs text-gray-600">{time}</span>,
    },
    {
      title: '更新时间',
      dataIndex: 'update_time',
      key: 'update_time',
      width: 180,
      sorter: true,
      sortOrder: getSortOrder('update_time'),
      render: (time: string) => <span className="text-xs text-gray-600">{time}</span>,
    },
    {
      title: '操作',
      key: 'action',
      width: 140,
      fixed: 'right',
      render: (_, record: AlertSend) => (
        <Space size="small">
          <Button type="link" size="small" icon={<EditOutlined />} onClick={() => onEdit(record)} className={tableStyles.actionButton}>
            编辑
          </Button>
          <Popconfirm title="确认删除" description="确定要删除这个告警发送配置吗？" onConfirm={() => onDelete(record.id)} okText="确定" cancelText="取消">
            <Button type="link" size="small" danger icon={<DeleteOutlined />} className={tableStyles.actionButton}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];
};
