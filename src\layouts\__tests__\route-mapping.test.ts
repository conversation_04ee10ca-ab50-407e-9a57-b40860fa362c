import { describe, it, expect } from 'vitest';
import { ROUTE_CONFIG, ROUTE_TAB_MAPPING, TAB_LABELS, getPathByKey, getKeyByPath } from '../constants';

describe('路由映射逻辑测试', () => {
  describe('ROUTE_CONFIG', () => {
    it('应该包含所有必要的路由配置', () => {
      expect(ROUTE_CONFIG.dashboard).toEqual({ path: '/dashboard', label: '仪表板' });
      expect(ROUTE_CONFIG.users).toEqual({ path: '/users', label: '用户管理' });
      expect(ROUTE_CONFIG.tasks).toEqual({ path: '/tasks', label: '任务管理' });
      expect(ROUTE_CONFIG.alerts).toEqual({ path: '/alerts', label: '告警管理' });
      expect(ROUTE_CONFIG.teams).toEqual({ path: '/teams', label: '团队协作' });
    });
  });

  describe('ROUTE_TAB_MAPPING', () => {
    it('应该正确映射根路径到仪表板', () => {
      expect(ROUTE_TAB_MAPPING['/']).toEqual({ key: 'dashboard', label: '仪表板' });
    });

    it('应该正确映射所有路径', () => {
      expect(ROUTE_TAB_MAPPING['/dashboard']).toEqual({ key: 'dashboard', label: '仪表板' });
      expect(ROUTE_TAB_MAPPING['/users']).toEqual({ key: 'users', label: '用户管理' });
      expect(ROUTE_TAB_MAPPING['/tasks']).toEqual({ key: 'tasks', label: '任务管理' });
      expect(ROUTE_TAB_MAPPING['/alerts']).toEqual({ key: 'alerts', label: '告警管理' });
      expect(ROUTE_TAB_MAPPING['/teams']).toEqual({ key: 'teams', label: '团队协作' });
    });
  });

  describe('TAB_LABELS', () => {
    it('应该包含所有标签页标题', () => {
      expect(TAB_LABELS.dashboard).toBe('仪表板');
      expect(TAB_LABELS.users).toBe('用户管理');
      expect(TAB_LABELS.tasks).toBe('任务管理');
      expect(TAB_LABELS.alerts).toBe('告警管理');
      expect(TAB_LABELS.teams).toBe('团队协作');
    });
  });

  describe('getPathByKey', () => {
    it('应该正确返回对应的路径', () => {
      expect(getPathByKey('dashboard')).toBe('/dashboard');
      expect(getPathByKey('users')).toBe('/users');
      expect(getPathByKey('tasks')).toBe('/tasks');
      expect(getPathByKey('alerts')).toBe('/alerts');
      expect(getPathByKey('teams')).toBe('/teams');
    });

    it('应该为未知 key 返回默认路径', () => {
      expect(getPathByKey('unknown')).toBe('/dashboard');
    });
  });

  describe('getKeyByPath', () => {
    it('应该正确返回对应的 key', () => {
      expect(getKeyByPath('/')).toBe('dashboard');
      expect(getKeyByPath('/dashboard')).toBe('dashboard');
      expect(getKeyByPath('/users')).toBe('users');
      expect(getKeyByPath('/tasks')).toBe('tasks');
      expect(getKeyByPath('/alerts')).toBe('alerts');
      expect(getKeyByPath('/teams')).toBe('teams');
    });

    it('应该为未知路径返回默认 key', () => {
      expect(getKeyByPath('/unknown')).toBe('dashboard');
    });
  });

  describe('配置一致性检查', () => {
    it('ROUTE_TAB_MAPPING 应该与 ROUTE_CONFIG 保持一致', () => {
      Object.entries(ROUTE_CONFIG).forEach(([key, config]) => {
        expect(ROUTE_TAB_MAPPING[config.path]).toEqual({
          key,
          label: config.label,
        });
      });
    });

    it('TAB_LABELS 应该与 ROUTE_CONFIG 保持一致', () => {
      Object.entries(ROUTE_CONFIG).forEach(([key, config]) => {
        expect(TAB_LABELS[key]).toBe(config.label);
      });
    });

    it('getPathByKey 和 getKeyByPath 应该互为逆函数', () => {
      Object.keys(ROUTE_CONFIG).forEach(key => {
        const path = getPathByKey(key);
        expect(getKeyByPath(path)).toBe(key);
      });
    });
  });
});
