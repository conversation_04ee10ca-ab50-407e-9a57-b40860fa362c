import { FilterOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row, Select } from 'antd';
import React from 'react';

import type { TaskBasicSearchParams } from '../../types';
import { TASK_STATUS_OPTIONS, FREQUENCY_OPTIONS, WEEKDAY_OPTIONS } from '../../constants';
import { tableStyles } from '../../styles';
import { TaskGroupSelect } from '../common/TaskGroupSelect';

const { Option } = Select;

interface SearchFormProps {
  form: any;
  onSubmit: (values: TaskBasicSearchParams) => void;
  onReset: () => void;
  onAdvancedSearch: () => void;
}

/**
 * 快速搜索表单组件
 */
export const QuickSearchForm: React.FC<SearchFormProps> = ({ form, onSubmit, onReset, onAdvancedSearch }) => {
  return (
    <div className={tableStyles.searchSection}>
      <Form form={form} onFinish={onSubmit} className="w-full">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="name" className="mb-0">
                <Input placeholder="请输入任务名称" prefix={<SearchOutlined className="text-gray-400" />} allowClear className="rounded-md" autoComplete="off" autoCorrect="off" autoCapitalize="off" spellCheck={false} />
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="group_name" className="mb-0">
                <TaskGroupSelect placeholder="请选择任务分组" allowClear className="w-full rounded-md" dynamicSearch={true} />
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="status" className="mb-0">
                <Select placeholder="请选择任务状态" allowClear className="w-full rounded-md">
                  {TASK_STATUS_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </Col>
          <Col xs={24} sm={24} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <div className="flex gap-2 items-center h-full">
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />} className={tableStyles.buttonPrimary}>
                  搜索
                </Button>
                <Button onClick={onReset} className="rounded-md flex-1" icon={<ReloadOutlined />}>
                  重置
                </Button>
                <Button icon={<FilterOutlined />} onClick={onAdvancedSearch} className="rounded-md flex-1">
                  详细查询
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};

interface AdvancedSearchFormProps {
  form: any;
  onSubmit: (values: TaskBasicSearchParams) => void;
  onReset: () => void;
  searchParams: TaskBasicSearchParams;
}

/**
 * 详细查询表单组件
 */
export const AdvancedSearchForm: React.FC<AdvancedSearchFormProps> = ({ form, onSubmit, onReset, searchParams }) => {
  return (
    <Form form={form} layout="vertical" onFinish={onSubmit} initialValues={searchParams}>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="任务名称" name="name">
            <Input placeholder="请输入任务名称" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="任务分组" name="group">
            <TaskGroupSelect placeholder="请选择任务分组" allowClear dynamicSearch={true} />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="任务状态" name="status">
            <Select placeholder="请选择任务状态" allowClear>
              {TASK_STATUS_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="执行频率" name="frequency">
            <Select placeholder="请选择执行频率" allowClear>
              {FREQUENCY_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="星期" name="weekday">
            <Select placeholder="请选择星期" allowClear>
              {WEEKDAY_OPTIONS.map(option => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="告警接收人" name="alert_receiver">
            <Input placeholder="请输入告警接收人" />
          </Form.Item>
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="开始时间" name="start_time">
            <Input placeholder="HH:mm:ss" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="结束时间" name="end_time">
            <Input placeholder="HH:mm:ss" />
          </Form.Item>
        </Col>
      </Row>
      <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
        <Button onClick={onReset} className="rounded-md px-6 py-2">
          重置
        </Button>
        <Button type="primary" htmlType="submit" className={tableStyles.modalButtonPrimary}>
          查询
        </Button>
      </div>
    </Form>
  );
};
