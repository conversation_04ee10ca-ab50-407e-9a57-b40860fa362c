import React, { useEffect } from 'react';
import { Form, Input, Card, Row, Col, Button } from 'antd';
import type { FormInstance } from 'antd';

import type { OtherInfo } from '../../types';
import { formStyles } from '../../styles';
import { FORM_BUTTON_TEXT } from '../../constants';

interface OtherInfoBasicFormProps {
  form: FormInstance;
  initialData?: OtherInfo;
  onSubmit?: (values: OtherInfo) => void;
  onCancel?: () => void;
  onReset?: () => void;
  loading?: boolean;
}

/**
 * 其他信息基本信息表单组件
 * 用于新增和编辑其他信息配置
 */
export const OtherInfoBasicForm: React.FC<OtherInfoBasicFormProps> = ({ form, initialData, onSubmit, onCancel, onReset, loading = false }) => {
  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
    }
  }, [form, initialData]);

  // 表单提交处理
  const handleSubmit = (values: OtherInfo) => {
    console.log('其他信息表单提交:', values);
    onSubmit?.(values);
  };

  // 按钮点击提交处理
  const handleButtonSubmit = () => {
    form.submit();
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    onReset?.();
  };

  return (
    <div className="h-full flex flex-col">
      <Form form={form} layout="vertical" onFinish={handleSubmit} className="flex-1 overflow-auto">
        {/* 隐藏的 id 字段，用于编辑时保持 id */}
        <Form.Item name="id" hidden>
          <Input />
        </Form.Item>
        <div className={formStyles.tabContent}>
          <Card title="基本信息" className={formStyles.card}>
            <Row gutter={16} className="mb-4">
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="信息名称"
                  rules={[
                    { required: true, message: '请输入信息名称' },
                    { max: 50, message: '信息名称不能超过50个字符' },
                  ]}
                >
                  <Input placeholder="请输入信息名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="business"
                  label="业务系统名称"
                  rules={[
                    { required: true, message: '请输入业务系统名称' },
                    { max: 100, message: '业务系统名称不能超过100个字符' },
                  ]}
                >
                  <Input placeholder="请输入业务系统名称" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16} className="mb-4">
              <Col span={12}>
                <Form.Item
                  name="business_en"
                  label="业务系统英文名称"
                  rules={[
                    { required: true, message: '请输入业务系统英文名称' },
                    { max: 100, message: '业务系统英文名称不能超过100个字符' },
                    {
                      pattern: /^[a-zA-Z0-9_-]+$/,
                      message: '英文名称只能包含字母、数字、下划线和连字符',
                    },
                  ]}
                >
                  <Input placeholder="请输入业务系统英文名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="hostname"
                  label="主机名称"
                  rules={[
                    { required: true, message: '请输入主机名称' },
                    { max: 100, message: '主机名称不能超过100个字符' },
                  ]}
                >
                  <Input placeholder="请输入主机名称" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16} className="mb-4">
              <Col span={24}>
                <Form.Item
                  name="location"
                  label="告警来源"
                  rules={[
                    { required: true, message: '请输入告警来源' },
                    { max: 200, message: '告警来源不能超过200个字符' },
                  ]}
                >
                  <Input placeholder="请输入告警来源" />
                </Form.Item>
              </Col>
            </Row>
          </Card>
        </div>
      </Form>

      {/* 底部操作栏 */}
      <div className={formStyles.footerContainer}>
        <div className="flex justify-between items-center">
          <div className={formStyles.footerHint}>{initialData ? '编辑其他信息' : '创建新其他信息'}</div>
          <div className={formStyles.buttonGroup}>
            <Button onClick={onCancel} className={`${formStyles.actionButton} ${formStyles.cancelButton}`}>
              {FORM_BUTTON_TEXT.cancel}
            </Button>
            {!initialData && (
              <Button onClick={handleReset} className={`${formStyles.actionButton} ${formStyles.resetButton}`}>
                {FORM_BUTTON_TEXT.reset}
              </Button>
            )}
            <Button type="primary" loading={loading} onClick={handleButtonSubmit} className={`${formStyles.actionButton} ${initialData ? formStyles.confirmButton : formStyles.submitButton}`}>
              {initialData ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
