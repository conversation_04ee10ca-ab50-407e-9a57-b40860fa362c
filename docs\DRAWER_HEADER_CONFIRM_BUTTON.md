# 将确认选择按钮移到抽屉Header

## 修改概述

将选择发送功能中的"确认选择"按钮从表格底部的ActionButtons区域移动到抽屉的header最右边，提供更好的用户体验。

## 实现的修改

### 1. 抽屉Header自定义
- 在NotificationConfigTab中自定义了抽屉的title属性
- 使用flex布局将标题和确认按钮分别放在左右两侧
- 确认按钮显示选择数量，未选择时禁用

### 2. 选择状态管理
- 添加了`selectedRows`状态来跟踪当前选择的数据
- 通过`handleSelectionChange`函数接收来自AlertSendTable的选择变化
- 实时更新确认按钮的状态和显示文本

### 3. 移除表格底部的确认按钮
- 从AlertSendActionButtons组件中移除了确认选择按钮
- 清理了不再使用的CheckOutlined图标导入
- 简化了ActionButtons的逻辑

### 4. 选择变化通知机制
- 修改AlertSendTable的useSelection回调
- 在选择模式下，每次选择变化都会通知父组件
- 父组件实时更新selectedRows状态

## 修改的文件

### 1. NotificationConfigTab.tsx
```typescript
// 添加selectedRows状态管理
const [selectedRows, setSelectedRows] = useState<AlertSend[]>([]);

// 自定义抽屉header
<Drawer 
  title={
    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
      <span>选择告警发送配置</span>
      <Button 
        type="primary" 
        icon={<CheckOutlined />} 
        onClick={handleDrawerSelectionConfirm}
        disabled={selectedRows.length === 0}
      >
        确认选择 {selectedRows.length > 0 && `(${selectedRows.length})`}
      </Button>
    </div>
  }
  // ...其他属性
>
```

### 2. AlertSendTable.tsx
```typescript
// 修改选择变化回调
onSelectionChange: (selectedKeys: React.Key[], selectedRows: AlertSend[]) => {
  console.log('选择变化:', { selectedKeys, selectedRows });
  // 在选择模式下，通知父组件选择变化
  if (isSelectionMode && onSelectionConfirm) {
    onSelectionConfirm(selectedRows);
  }
},
```

### 3. AlertSendActionButtons.tsx
- 移除了确认选择按钮相关代码
- 清理了CheckOutlined图标导入
- 简化了组件逻辑

## 用户体验改进

### 1. 更直观的操作
- 确认按钮位于抽屉顶部，用户无需滚动到底部
- 按钮位置更符合用户的操作习惯

### 2. 实时反馈
- 按钮文本显示当前选择的数量
- 未选择任何项时按钮自动禁用
- 选择状态变化时按钮状态实时更新

### 3. 清晰的界面布局
- 抽屉header布局更加平衡
- 操作按钮位置更加突出
- 减少了表格底部的按钮混乱

## 技术特点

### 1. 状态同步
- 选择状态在AlertSendTable和NotificationConfigTab之间实时同步
- 使用回调函数机制确保数据一致性

### 2. 组件解耦
- AlertSendTable专注于数据展示和选择
- NotificationConfigTab负责确认操作和状态管理
- 各组件职责清晰，便于维护

### 3. 类型安全
- 所有状态和回调都有明确的TypeScript类型定义
- 编译时检查确保代码质量

## 测试建议

1. 测试抽屉打开时确认按钮的初始状态
2. 测试选择数据时按钮状态的变化
3. 测试取消选择时按钮状态的更新
4. 测试确认选择功能的正确性
5. 测试按钮禁用状态的交互体验

## 总结

通过将确认选择按钮移到抽屉header，我们实现了：
- 更好的用户体验
- 更清晰的界面布局
- 更直观的操作流程
- 实时的状态反馈

这个修改使得选择发送功能更加完善和用户友好。
