# 修复选择状态同步问题

## 问题描述

在选择发送功能中，当用户执行以下操作序列时出现状态同步问题：

1. 用户选择了一些告警发送配置
2. 点击"清空选择"按钮，`selectedRows`变为空数组
3. 再次点击"选择发送"按钮
4. **问题**: AlertSendTable中的多选框没有正确同步，应该预选中当前的`alertSends`

## 问题原因分析

### 1. 状态更新时序问题
```typescript
// NotificationConfigTab.tsx - 原始逻辑
const handleSelectAlertSend = () => {
  setSelectedRows(alertSends);  // 异步状态更新
  setDrawerVisible(true);       // 立即执行
};

// AlertSendTable渲染时
<AlertSendTable 
  selectedKeys={selectedRows.map(row => row.id)}  // 此时selectedRows可能还是空数组
/>
```

### 2. useSelection Hook的局限性
- `initialSelectedKeys`只在组件初始化时生效
- 后续的`selectedKeys`变化没有被正确处理
- 缺少对外部`selectedKeys`变化的响应机制

## 解决方案

### 1. 添加useEffect同步机制
在NotificationConfigTab中添加useEffect来确保状态同步：

```typescript
// 当抽屉打开时，确保selectedRows与alertSends同步
useEffect(() => {
  if (drawerVisible) {
    setSelectedRows(alertSends);
  }
}, [drawerVisible, alertSends]);
```

### 2. 简化选择发送逻辑
```typescript
// 简化后的逻辑
const handleSelectAlertSend = () => {
  // 打开抽屉，useEffect会自动同步selectedRows
  setDrawerVisible(true);
};
```

### 3. 增强useSelection Hook
修改useSelection hook以正确处理`initialSelectedKeys`的变化：

```typescript
// 处理初始选择的keys和外部selectedKeys变化
useEffect(() => {
  if (initialSelectedKeys.length > 0 && data.length > 0) {
    const selectedRowsFromKeys = data.filter(item => initialSelectedKeys.includes(item.id));
    
    if (crossPage) {
      // 跨页面模式：重新设置全局选择状态
      const newMap = new Map<React.Key, T>();
      selectedRowsFromKeys.forEach(row => {
        newMap.set(row.id, row);
      });
      setAllSelectedRows(newMap);
    }
    
    // 更新当前页面选择状态
    const currentPageSelectedKeys = selectedRowsFromKeys.map(item => item.id);
    setSelection({
      selectedRowKeys: currentPageSelectedKeys,
      selectedRows: selectedRowsFromKeys,
    });
  } else if (initialSelectedKeys.length === 0) {
    // 如果initialSelectedKeys为空，清空选择状态
    if (crossPage) {
      setAllSelectedRows(new Map());
    }
    setSelection({
      selectedRowKeys: [],
      selectedRows: [],
    });
  }
}, [initialSelectedKeys, data, crossPage]);
```

## 修复效果

### 1. 状态同步正确
- 点击"选择发送"后，AlertSendTable正确预选中当前的`alertSends`
- 清空选择后再次选择发送，状态正确重置和同步

### 2. 用户体验改善
- 用户操作流程更加直观
- 避免了状态不一致导致的困惑
- 多选框状态与实际数据保持一致

### 3. 代码健壮性提升
- 处理了边界情况（空数组的情况）
- 增强了useSelection hook的通用性
- 减少了状态管理的复杂度

## 技术实现细节

### 1. 状态流向
```
用户点击"选择发送"
  ↓
setDrawerVisible(true)
  ↓
useEffect监听到drawerVisible变化
  ↓
setSelectedRows(alertSends)
  ↓
AlertSendTable接收到新的selectedKeys
  ↓
useSelection hook处理selectedKeys变化
  ↓
更新表格选择状态
```

### 2. 关键改进点
- **时序控制**: 使用useEffect确保状态更新的正确时序
- **响应式更新**: useSelection hook现在能响应外部selectedKeys的变化
- **边界处理**: 正确处理空数组和非空数组的情况

### 3. 兼容性保证
- 不影响现有的选择功能
- 保持跨页面选择的完整性
- 维持所有原有的交互逻辑

## 测试场景

### 1. 基本流程测试
1. 选择一些告警发送配置
2. 点击"清空选择"
3. 点击"选择发送"
4. 验证表格中的多选框是否正确预选中

### 2. 边界情况测试
1. 在没有任何配置时点击"选择发送"
2. 在有配置但未选择时点击"选择发送"
3. 验证各种情况下的状态是否正确

### 3. 交互连续性测试
1. 多次执行选择-清空-选择的操作
2. 验证每次操作后的状态是否正确
3. 确认没有状态残留问题

## 总结

通过添加useEffect同步机制和增强useSelection hook的响应能力，成功解决了选择状态同步问题。这个修复：

- **解决了核心问题**: 选择发送时的状态同步
- **提升了用户体验**: 操作更加直观和一致
- **增强了代码健壮性**: 处理了各种边界情况
- **保持了向后兼容**: 不影响现有功能

修复后，用户在执行"清空选择"后再次"选择发送"时，表格会正确显示当前应该选中的项目。
