import OtherInfoTable from '@/components/SqlMonitor/components/OtherInfoTable';
import React from 'react';
import { useOutletContext } from 'react-router-dom';

interface OutletContext {
  contentHeight: number;
}

/**
 * 其他信息管理页面
 * 展示完整的其他信息管理表格功能
 */
const OtherInfoTablePage: React.FC = () => {
  const { contentHeight } = useOutletContext<OutletContext>();
  return <OtherInfoTable contentHeight={contentHeight} />;
};

export default OtherInfoTablePage;
