import { create } from 'zustand';
import type { AlertSend } from '../types';

// 定义选中行状态的泛型接口
interface SelectedRowsState<T> {
  // 选中的行数据
  selectedRows: T[];

  // 设置选中的行
  setSelectedRows: (rows: T[]) => void;

  // 移除选中行
  removeSelectedRow: (rowId: number) => void;

  // 获取选择行
  getSelectedRows: () => T[];
}

export const useSelectedRowsInDrawerStore = create<SelectedRowsState<AlertSend>>((set, get) => ({
  selectedRows: [],

  // 设置选中的行数据
  setSelectedRows: (rows: AlertSend[]) =>
    set({
      selectedRows: rows,
    }),

  // 移除选中行
  removeSelectedRow: (rowId: number) =>
    set(state => ({
      selectedRows: state.selectedRows.filter(r => r.id !== rowId),
    })),

  // 获取选择行
  getSelectedRows: () => get().selectedRows,
}));
