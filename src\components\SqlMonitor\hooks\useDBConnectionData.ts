/**
 * 数据库连接数据管理Hook
 * 管理数据库连接列表的加载、搜索、分页等状态
 */

import { useState, useCallback, useEffect } from 'react';
import { App } from 'antd';
import type { DBConnection, DBConnectionSearchParams } from '../types';
import { TaskService } from '../services';
import { DEFAULT_PAGINATION } from '../constants';

export interface UseDBConnectionDataOptions {
  /** 是否自动加载数据 */
  autoLoad?: boolean;
  /** 初始搜索参数 */
  initialParams?: DBConnectionSearchParams;
}

export interface UseDBConnectionDataReturn {
  /** 数据库连接列表数据 */
  data: DBConnection[];
  /** 数据加载状态 */
  loading: boolean;
  /** 数据总数 */
  total: number;
  /** 分页状态 */
  pagination: {
    current: number;
    page_size: number;
  };
  /** 搜索参数 */
  searchParams: DBConnectionSearchParams;
  /** 加载数据方法 */
  loadData: (customParams?: Partial<DBConnectionSearchParams>) => Promise<void>;
  /** 刷新数据方法 */
  refreshData: () => Promise<void>;
  /** 重置数据方法 */
  resetData: () => void;
  /** 更新搜索参数方法 */
  updateSearchParams: (params: DBConnectionSearchParams) => void;
  /** 更新分页方法 */
  updatePagination: (current: number, pageSize: number) => void;
}

/**
 * 数据库连接数据管理Hook
 */
export function useDBConnectionData(options: UseDBConnectionDataOptions = {}): UseDBConnectionDataReturn {
  const { autoLoad = true, initialParams = {} } = options;
  const { message } = App.useApp();

  // 状态管理
  const [data, setData] = useState<DBConnection[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [pagination, setPagination] = useState(DEFAULT_PAGINATION);
  const [searchParams, setSearchParams] = useState<DBConnectionSearchParams>(initialParams);

  // 加载数据方法
  const loadData = useCallback(
    async (customParams?: Partial<DBConnectionSearchParams>) => {
      setLoading(true);
      try {
        // 合并搜索参数
        const finalParams = {
          ...searchParams,
          ...pagination,
          ...customParams,
        };

        console.log('加载数据库连接数据，参数:', finalParams);

        const page_size = finalParams.page_size || DEFAULT_PAGINATION.page_size;
        const current = finalParams.current || DEFAULT_PAGINATION.current;
        // 更新分页状态
        setPagination({
          current,
          page_size,
        });

        // 调用API获取数据
        const response = await TaskService.getDbConnections(finalParams);

        if (!response || !response.data || response.data == null) {
          console.warn('获取数据库连接数据失败：返回数据为空');
          message.warning('未获取到数据库连接数据');
          setData([]);
          setTotal(0);
          return;
        }

        setData(response.data);
        setTotal(response.total);

        console.log('数据库连接数据加载成功:', response);
      } catch (error) {
        console.error('加载数据库连接数据失败:', error);
        message.error('加载数据失败');
        setData([]);
        setTotal(0);
      } finally {
        setLoading(false);
      }
    },
    [searchParams, pagination, message]
  );

  // 刷新数据方法
  const refreshData = useCallback(async () => {
    await loadData();
  }, [loadData]);

  // 重置数据方法
  const resetData = useCallback(() => {
    setSearchParams({});
    setPagination(DEFAULT_PAGINATION);
    // setData([]);
    // setTotal(0);
  }, []);

  // 更新搜索参数方法
  const updateSearchParams = useCallback((params: DBConnectionSearchParams) => {
    setSearchParams(params);
  }, []);

  // 更新分页方法
  const updatePagination = useCallback((current: number, pageSize: number) => {
    setPagination({ current, page_size: pageSize });
  }, []);

  // 自动加载数据
  useEffect(() => {
    if (autoLoad) {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [autoLoad]);

  return {
    data,
    loading,
    total,
    pagination,
    searchParams,
    loadData,
    refreshData,
    resetData,
    updateSearchParams,
    updatePagination,
  };
}
