import React from 'react';
import VTable from './VTable';

/**
 * VTable 空数据状态测试组件
 * 用于验证空数据时的样式和交互功能
 */
const VTableEmptyStateTest: React.FC = () => {
  return (
    <div style={{ padding: '20px' }}>
      <h1
        style={{
          marginBottom: '20px',
          fontSize: '24px',
          fontWeight: 'bold',
        }}
      >
        VTable 空数据状态测试
      </h1>

      <div
        style={{
          marginBottom: '20px',
        }}
      >
        <h2
          style={{
            fontSize: '18px',
            marginBottom: '10px',
          }}
        >
          测试说明：
        </h2>
        <ul
          style={{
            paddingLeft: '20px',
            lineHeight: '1.6',
          }}
        >
          <li>✅ 组件初始化时应显示空数据状态</li>
          <li>✅ 空数据时应显示友好的提示信息和图标</li>
          <li>✅ 空数据时应隐藏不相关的操作按钮（如全选、取消全选等）</li>
          <li>✅ 空数据时应显示"生成示例数据"按钮</li>
          <li>✅ 点击"生成示例数据"后应正常加载数据并切换到正常状态</li>
          <li>✅ 数据状态标签应正确显示"暂无数据"</li>
          <li>✅ 清空所有数据后应重新回到空数据状态</li>
        </ul>
      </div>

      <div
        style={{
          border: '2px solid #e5e7eb',
          borderRadius: '8px',
          overflow: 'hidden',
        }}
      >
        <VTable />
      </div>
    </div>
  );
};

export default VTableEmptyStateTest;
