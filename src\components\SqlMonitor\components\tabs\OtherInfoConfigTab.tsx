import React from 'react';
import { Card, Button, Space, Row, Col } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { OtherInfo } from '../../types';
import { formStyles } from '../../styles';

interface OtherInfoConfigTabProps {
  otherInfo: OtherInfo | null;
  onOtherInfoChange: (otherInfo: OtherInfo | null) => void;
  onAddOtherInfo: () => void;
  onEditOtherInfo: () => void;
  onSelectOtherInfo: () => void;
}

/**
 * 其他信息配置标签页组件
 * 管理附加信息的配置
 */
const OtherInfoConfigTab: React.FC<OtherInfoConfigTabProps> = ({ otherInfo, onOtherInfoChange, onAddOtherInfo, onEditOtherInfo, onSelectOtherInfo }) => {
  const handleDeleteOtherInfo = () => {
    onOtherInfoChange(null);
  };

  return (
    <div className={formStyles.tabContent}>
      <Card
        title="附加信息配置"
        size="small"
        className="mb-4"
        extra={
          <Space>
            <Button type="primary" size="small" icon={<PlusOutlined />} onClick={onAddOtherInfo}>
              新增信息
            </Button>
            <Button size="small" icon={<EyeOutlined />} onClick={onSelectOtherInfo}>
              选择信息
            </Button>
          </Space>
        }
      >
        {otherInfo ? (
          <div
            style={{
              padding: '16px',
              background: '#f5f5f5',
              borderRadius: '8px',
            }}
          >
            <Row gutter={16}>
              <Col span={8}>
                <div>
                  <strong>信息名称:</strong> {otherInfo.name}
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <strong>业务系统:</strong> {otherInfo.business}
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <strong>英文名称:</strong> {otherInfo.business_en}
                </div>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: '8px' }}>
              <Col span={8}>
                <div>
                  <strong>主机名称:</strong> {otherInfo.hostname}
                </div>
              </Col>
              <Col span={8}>
                <div>
                  <strong>告警来源:</strong> {otherInfo.location}
                </div>
              </Col>
              <Col span={8}>
                <Space>
                  <Button type="text" size="small" icon={<EditOutlined />} onClick={onEditOtherInfo}>
                    编辑
                  </Button>
                  <Button type="text" size="small" danger icon={<DeleteOutlined />} onClick={handleDeleteOtherInfo}>
                    删除
                  </Button>
                </Space>
              </Col>
            </Row>
          </div>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>暂无附加信息配置，请点击"新增信息"或"选择信息"添加</div>
        )}
      </Card>
    </div>
  );
};

export default OtherInfoConfigTab;
