import AlertSendTable from '@/components/SqlMonitor/components/AlertSendTable';
import React from 'react';
import { useOutletContext } from 'react-router-dom';

interface OutletContext {
  contentHeight: number;
}

/**
 * 告警发送管理页面
 * 展示完整的告警发送管理表格功能
 */
const AlertSendTablePage: React.FC = () => {
  const { contentHeight } = useOutletContext<OutletContext>();
  return <AlertSendTable contentHeight={contentHeight} />;
};

export default AlertSendTablePage;
