/**
 * 表单相关类型定义
 */

/**
 * 表单操作类型
 */
export type FormAction = 'add' | 'edit' | 'view';

/**
 * 表单状态类型
 */
export type FormState = 'idle' | 'loading' | 'submitting' | 'success' | 'error';

/**
 * 表单验证状态类型
 */
export type FormValidateStatus = 'success' | 'warning' | 'error' | 'validating';

/**
 * 表单字段类型
 */
export interface FormField {
  name: string;
  label: string;
  type: 'input' | 'select' | 'textarea' | 'number' | 'date' | 'time' | 'switch' | 'checkbox' | 'radio';
  required?: boolean;
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
  rules?: any[];
  disabled?: boolean;
  hidden?: boolean;
}

/**
 * 表单配置类型
 */
export interface FormConfig {
  layout?: 'horizontal' | 'vertical' | 'inline';
  labelCol?: { span: number };
  wrapperCol?: { span: number };
  size?: 'small' | 'middle' | 'large';
  disabled?: boolean;
}

/**
 * 表单提交数据类型
 */
export interface FormSubmitData {
  [key: string]: any;
}

/**
 * 表单验证错误类型
 */
export interface FormError {
  field: string;
  message: string;
}

/**
 * 表单回调函数类型
 */
export interface FormCallbacks {
  onSubmit?: (data: FormSubmitData) => void | Promise<void>;
  onCancel?: () => void;
  onReset?: () => void;
  onChange?: (field: string, value: any) => void;
  onValidate?: (errors: FormError[]) => void;
}
