# DefaultLayout 重构说明

## 重构概述

本次重构将原本的大型 `DefaultLayout.tsx` 组件（397行）拆分为更小的、可重用的组件和自定义 hooks，提高了代码的可维护性和可读性。

## 文件结构

```
src/layouts/
├── DefaultLayout.tsx          # 主布局组件（重构后仅73行）
├── DefaultLayout.module.css   # 样式文件（保持不变）
├── types.ts                   # TypeScript 类型定义
├── constants.ts               # 配置常量
├── README.md                  # 本说明文档
├── components/                # 子组件目录
│   ├── index.ts              # 组件导出文件
│   ├── Header.tsx            # 头部导航栏组件
│   ├── Sidebar.tsx           # 侧边栏组件
│   └── TabsContainer.tsx     # 标签页容器组件
└── hooks/                     # 自定义 hooks 目录
    ├── index.ts              # hooks 导出文件
    ├── useTabs.ts            # 标签页管理 hook
    ├── useBreadcrumb.ts      # 面包屑导航 hook
    ├── useContentHeight.ts   # 内容区域高度管理 hook
    └── useSelectedKeys.ts    # 菜单选中状态 hook
```

## 重构内容

### 1. 类型定义 (`types.ts`)

- `TabItem`: 标签页项目接口
- `MenuItemConfig`: 菜单项配置接口
- `RouteMapping`: 路由映射接口
- `BreadcrumbItem`: 面包屑项目接口
- `LayoutContextType`: 布局上下文接口
- `HeaderProps`, `SidebarProps`, `TabsContainerProps`: 组件属性接口
- `UserInfo`: 用户信息接口

### 2. 配置常量 (`constants.ts`)

- `ROUTE_TAB_MAPPING`: 路由与标签页的映射关系
- `TAB_LABELS`: 标签页标题映射
- `getSidebarMenuItems()`: 侧边栏菜单配置函数
- `getUserMenuItems()`: 用户下拉菜单配置函数
- `DEFAULT_USER`: 默认用户信息
- `LAYOUT_CONSTANTS`: 布局常量

**注意**: 菜单配置使用函数形式而非常量，避免在 TypeScript 文件中直接使用 JSX 语法导致的解析问题。

### 3. 自定义 Hooks

#### `useTabs.ts`

管理标签页的状态和操作：

- 标签页的添加、删除、切换
- 根据路由自动初始化标签页
- 处理菜单点击事件

#### `useBreadcrumb.ts`

管理面包屑导航：

- 根据当前路由生成面包屑项目
- 使用 useMemo 优化性能

#### `useContentHeight.ts`

管理内容区域高度：

- 监听窗口大小变化
- 使用 ResizeObserver 监听元素大小变化
- 防抖优化性能

#### `useSelectedKeys.ts`

管理菜单选中状态：

- 根据当前路由确定选中的菜单项
- 使用 useMemo 优化性能

### 4. 子组件

#### `Header.tsx`

头部导航栏组件，包含：

- 折叠按钮
- 面包屑导航
- 通知铃铛
- 用户下拉菜单

#### `Sidebar.tsx`

侧边栏组件，包含：

- Logo 区域
- 导航菜单
- 折叠状态处理

#### `TabsContainer.tsx`

标签页容器组件，包含：

- 标签页显示
- 标签页切换
- 标签页关闭

## 重构优势

### 1. 代码可维护性

- 单一职责原则：每个组件和 hook 只负责特定功能
- 代码分离：逻辑、样式、配置分离
- 易于测试：小组件更容易编写单元测试

### 2. 代码复用性

- 组件可以在其他地方复用
- hooks 可以在其他组件中使用
- 配置可以轻松修改和扩展

### 3. 类型安全

- 完整的 TypeScript 类型定义
- 编译时错误检查
- 更好的 IDE 支持

### 4. 性能优化

- 使用 useMemo 和 useCallback 优化性能
- 防抖处理减少不必要的计算
- 组件拆分减少重渲染范围

## 使用方式

重构后的使用方式与之前完全相同：

```tsx
import { DefaultLayout } from './layouts/DefaultLayout';

// 或者使用别名（向后兼容）
import { ResponsiveLayout } from './layouts/DefaultLayout';
```

## 修复说明

### JSX 语法问题修复

在重构过程中，发现在 TypeScript 配置文件中直接使用 JSX 语法会导致解析错误。已进行以下修复：

1. **constants.ts**: 将菜单配置从常量改为函数形式
   - `SIDEBAR_MENU_ITEMS` → `getSidebarMenuItems()`
   - `USER_MENU_ITEMS` → `getUserMenuItems()`
   - 使用 `React.createElement()` 替代 JSX 语法

2. **useBreadcrumb.ts**: 修复面包屑导航中的 JSX 语法
   - 使用 `React.createElement()` 替代 JSX 语法
   - 确保在 TypeScript 文件中的类型安全

这些修复确保了代码在开发和构建过程中都能正常工作，避免了 ESBuild 解析错误。

## 扩展指南

### 添加新的菜单项

1. 在 `constants.ts` 中的 `getSidebarMenuItems()` 函数中添加菜单项
2. 在 `ROUTE_TAB_MAPPING` 和 `TAB_LABELS` 中添加对应映射
3. 在 `useTabs.ts` 的 `handleMenuClick` 中添加路由处理

### 添加新的组件

1. 在 `components/` 目录下创建新组件
2. 在 `types.ts` 中定义组件的 Props 接口
3. 在 `components/index.ts` 中导出新组件

### 添加新的 Hook

1. 在 `hooks/` 目录下创建新 hook
2. 在 `hooks/index.ts` 中导出新 hook
3. 在主组件中使用新 hook
