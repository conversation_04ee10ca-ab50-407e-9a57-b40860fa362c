import React, { useState } from 'react';
import { Card, Statistic, Row, Col, Button, Table, Tag, Space } from 'antd';
import { DatabaseOutlined, SendOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { mockDbConnectionData, mockAlertSendData, mockOtherInfoData } from '../services/taskService';
import type { AlertSend } from '../types/task';

const MockDataDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'db' | 'alert' | 'other'>('db');

  // 数据库连接表格列
  const dbColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '连接名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '数据库类型',
      dataIndex: 'db_type',
      key: 'db_type',
      render: (type: string) => <Tag color={type === 'mysql' ? 'blue' : 'orange'}>{type.toUpperCase()}</Tag>,
    },
    {
      title: '主机地址',
      dataIndex: 'host',
      key: 'host',
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
      width: 80,
    },
    {
      title: '用户名',
      dataIndex: 'user',
      key: 'user',
    },
    {
      title: '数据库名',
      dataIndex: 'database',
      key: 'database',
      render: (database: string) => database || '-',
    },
  ];

  // 告警发送表格列
  const alertColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '发送名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '发送类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => <Tag color={type === 'kafka' ? 'green' : 'purple'}>{type.toUpperCase()}</Tag>,
    },
    {
      title: '接收地址',
      key: 'address',
      render: (_: string, record: AlertSend) => {
        const address = record.type === 'kafka' ? record.kafka_receiver.address : record.prometheus_receiver.address;
        return <span style={{ fontSize: '12px' }}>{address}</span>;
      },
      ellipsis: true,
    },
    {
      title: 'Topic',
      key: 'topic',
      render: (_: string, record: AlertSend) => {
        return record.type === 'kafka' ? record.kafka_receiver.topic : '-';
      },
    },
  ];

  // 其他信息表格列
  const otherColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '信息名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
    },
    {
      title: '业务系统',
      dataIndex: 'business',
      key: 'business',
    },
    {
      title: '英文名称',
      dataIndex: 'business_en',
      key: 'business_en',
    },
    {
      title: '主机名称',
      dataIndex: 'hostname',
      key: 'hostname',
    },
    {
      title: '告警来源',
      dataIndex: 'location',
      key: 'location',
    },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'db':
        return (
          <Table
            dataSource={mockDbConnectionData}
            columns={dbColumns}
            rowKey="id"
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ y: 600 }}
            size="small"
          />
        );
      case 'alert':
        return (
          <Table
            dataSource={mockAlertSendData}
            columns={alertColumns}
            rowKey="id"
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ y: 600 }}
            size="small"
          />
        );
      case 'other':
        return (
          <Table
            dataSource={mockOtherInfoData}
            columns={otherColumns}
            rowKey="id"
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            }}
            scroll={{ y: 600 }}
            size="small"
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-6">模拟数据演示</h1>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={8}>
          <Card>
            <Statistic title="数据库连接" value={mockDbConnectionData.length} prefix={<DatabaseOutlined />} suffix="条" />
            <div className="mt-2 text-sm text-gray-500">
              MySQL: {mockDbConnectionData.filter(db => db.db_type === 'mysql').length} | Oracle: {mockDbConnectionData.filter(db => db.db_type === 'oracle').length}
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic title="告警发送" value={mockAlertSendData.length} prefix={<SendOutlined />} suffix="条" />
            <div className="mt-2 text-sm text-gray-500">
              Kafka: {mockAlertSendData.filter(alert => alert.type === 'kafka').length} | Prometheus: {mockAlertSendData.filter(alert => alert.type === 'prometheus').length}
            </div>
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic title="其他信息" value={mockOtherInfoData.length} prefix={<InfoCircleOutlined />} suffix="条" />
            <div className="mt-2 text-sm text-gray-500">业务系统: {[...new Set(mockOtherInfoData.map(info => info.business))].length} 种</div>
          </Card>
        </Col>
      </Row>

      {/* 切换按钮 */}
      <Space className="mb-4">
        <Button type={activeTab === 'db' ? 'primary' : 'default'} icon={<DatabaseOutlined />} onClick={() => setActiveTab('db')}>
          数据库连接
        </Button>
        <Button type={activeTab === 'alert' ? 'primary' : 'default'} icon={<SendOutlined />} onClick={() => setActiveTab('alert')}>
          告警发送
        </Button>
        <Button type={activeTab === 'other' ? 'primary' : 'default'} icon={<InfoCircleOutlined />} onClick={() => setActiveTab('other')}>
          其他信息
        </Button>
      </Space>

      {/* 数据表格 */}
      <Card>{renderContent()}</Card>
    </div>
  );
};

export default MockDataDemo;
