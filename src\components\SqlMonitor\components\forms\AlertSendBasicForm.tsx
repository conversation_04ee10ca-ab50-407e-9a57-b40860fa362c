import React, { useEffect, useMemo, useRef } from 'react';
import { Form, Input, Select, Card, Row, Col, Button } from 'antd';
import type { FormInstance } from 'antd';

import type { AlertSend } from '../../types';
import { formStyles } from '../../styles';
import { FORM_BUTTON_TEXT } from '../../constants';

const { TextArea } = Input;

const { Option } = Select;

interface AlertSendBasicFormProps {
  form: FormInstance;
  initialData?: AlertSend;
  onSubmit?: (values: AlertSend) => void;
  onCancel?: () => void;
  onReset?: () => void;
  loading?: boolean;
}

/**
 * 告警发送基本信息表单组件
 * 用于新增和编辑告警发送配置
 */
export const AlertSendBasicForm: React.FC<AlertSendBasicFormProps> = ({ form, initialData, onSubmit, onCancel, onReset, loading = false }) => {
  // 生成唯一的表单ID，避免DOM id冲突
  const formId = useMemo(() => `alert-send-form-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`, []);

  // 监听表单值变化，动态显示不同类型的配置
  const typeValue = Form.useWatch('receive_type', form);

  // 用于跟踪是否是首次渲染
  const isFirstRender = useRef(true);
  const previousTypeValue = useRef(typeValue);

  // 初始化表单数据
  useEffect(() => {
    if (initialData) {
      const formData = {
        ...initialData,
        // 如果 config 是对象，转换为格式化的 JSON 字符串
        // config: typeof initialData.config === 'object' ? JSON.stringify(initialData.config, null, 2) : initialData.config,
        config: JSON.stringify(JSON.parse(initialData.config), null, 2),
      };
      console.log('设置表单初始数据:', formData);
      form.setFieldsValue(formData);

      // 验证设置是否成功
      setTimeout(() => {
        const currentValues = form.getFieldsValue();
        console.log('当前表单值:', currentValues);
      }, 100);
    }
  }, [form, initialData]);

  // 监听 initialData 变化，重置内部状态
  useEffect(() => {
    // 当 initialData 变为 null 或 undefined 时，重置内部状态
    if (!initialData) {
      isFirstRender.current = true;
      previousTypeValue.current = undefined;
      console.log('抽屉关闭，重置表单内部状态');
    }
  }, [initialData]);

  // 表单提交处理
  const handleSubmit = (values: AlertSend) => {
    console.log('告警发送表单提交:', values);

    // 处理 config 字段，如果是字符串则尝试解析为对象
    const processedValues = {
      ...values,
      config:
        typeof values.config === 'string'
          ? (() => {
              try {
                return JSON.parse(values.config);
              } catch (e) {
                console.warn('配置信息格式不正确，保持原始字符串:', e);
                return values.config;
              }
            })()
          : values.config,
    };

    onSubmit?.(processedValues);
  };

  // 按钮点击提交处理
  const handleButtonSubmit = () => {
    form.submit();
  };

  // 重置表单
  const handleReset = () => {
    form.resetFields();
    onReset?.();
  };

  return (
    <div className="h-full flex flex-col">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        className="flex-1 overflow-auto"
        initialValues={
          {
            // 新增模式下不设置默认值，让用户自己选择
          }
        }
        id={formId}
      >
        {/* 隐藏的 id 字段，用于编辑时保持 id */}
        <Form.Item name="id" hidden>
          <Input id={`${formId}-id`} />
        </Form.Item>
        <div className={formStyles.tabContent}>
          <Card title="基本信息" className="mb-4!">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="发送名称"
                  rules={[
                    { required: true, message: '请输入发送名称' },
                    { max: 50, message: '发送名称不能超过50个字符' },
                  ]}
                >
                  <Input id={`${formId}-name`} placeholder="请输入发送名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="receive_type" label="接收类型" rules={[{ required: true, message: '请选择接收类型' }]}>
                  <Select placeholder="请选择接收类型">
                    <Option value="kafka">Kafka</Option>
                    <Option value="prometheus">Prometheus</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Card>

          <Card className="mb-4!">
            <Form.Item name="config" label="配置信息" rules={[{ required: true, message: '请输入配置信息' }]}>
              <TextArea placeholder="请输入配置信息" showCount autoSize={{ minRows: 4, maxRows: 10 }} style={{ resize: 'vertical' }} spellCheck={false} />
            </Form.Item>
          </Card>
        </div>
      </Form>
      {/* 操作按钮 */}
      <div className={formStyles.footerContainer}>
        <div className="flex justify-between items-center">
          <div className={formStyles.footerHint}>{initialData ? '编辑告警发送' : '创建新告警发送'}</div>
          <div className={formStyles.buttonGroup}>
            <Button onClick={onCancel} className={`${formStyles.actionButton} ${formStyles.cancelButton}`}>
              {FORM_BUTTON_TEXT.cancel}
            </Button>
            {!initialData && (
              <Button onClick={handleReset} className={`${formStyles.actionButton} ${formStyles.resetButton}`}>
                {FORM_BUTTON_TEXT.reset}
              </Button>
            )}
            <Button type="primary" loading={loading} onClick={handleButtonSubmit} className={`${formStyles.actionButton} ${initialData ? formStyles.confirmButton : formStyles.submitButton}`}>
              {initialData ? FORM_BUTTON_TEXT.update : FORM_BUTTON_TEXT.submit}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
