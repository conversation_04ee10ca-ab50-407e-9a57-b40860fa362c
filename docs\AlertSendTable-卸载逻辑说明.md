# AlertSendTable 组件卸载逻辑说明

## 概述

AlertSendTable 组件实现了完整的卸载清理逻辑，确保组件在卸载时能够正确清理所有资源和状态，防止内存泄漏和状态更新错误。

## 实现的卸载功能

### 1. 卸载标志管理

```typescript
// 组件卸载标志，用于防止在组件卸载后执行状态更新
const isUnmountedRef = useRef(false);
```

- 使用 `useRef` 创建卸载标志
- 在组件卸载时设置为 `true`
- 在异步操作中检查此标志，防止在组件卸载后更新状态

### 2. 主要清理逻辑

```typescript
useEffect(() => {
  return () => {
    console.log('AlertSendTable 组件卸载，开始执行清理操作');
    
    // 设置卸载标志
    isUnmountedRef.current = true;
    
    try {
      // 1. 清理表单状态
      searchForm.resetFields();
      editForm.resetFields();
      
      // 2. 清理选择状态
      clearSelection();
      
      // 3. 清理抽屉状态
      hideEditDrawer();
      
      // 4. 重置组件内部状态
      setCurrentRecord(null);
      setSubmitLoading(false);
      
      console.log('AlertSendTable 组件清理完成');
    } catch (error) {
      console.error('组件清理过程中发生错误:', error);
    }
  };
}, [searchForm, editForm, clearSelection, hideEditDrawer]);
```

### 3. 异步操作保护

在所有异步操作中添加卸载检查：

#### 删除操作
```typescript
const handleDelete = useCallback(async (id: number) => {
  try {
    await AlertSendService.deleteAlertSend(id);
    
    // 检查组件是否已卸载
    if (isUnmountedRef.current) {
      console.log('组件已卸载，跳过状态更新');
      return;
    }
    
    message.success('删除成功');
    await refreshData();
    clearSelection();
  } catch (error) {
    if (!isUnmountedRef.current) {
      message.error('删除失败');
    }
  }
}, [message, refreshData, clearSelection]);
```

#### 批量删除操作
```typescript
onOk: async () => {
  try {
    const deletePromises = selectedRows.map(row => AlertSendService.deleteAlertSend(row.id));
    await Promise.all(deletePromises);
    
    // 检查组件是否已卸载
    if (isUnmountedRef.current) {
      console.log('组件已卸载，跳过批量删除后的状态更新');
      return;
    }
    
    message.success(`成功删除 ${selectedRows.length} 条记录`);
    await refreshData();
    clearSelection();
  } catch (error) {
    if (!isUnmountedRef.current) {
      message.error('批量删除失败');
    }
  }
}
```

#### 表单提交操作
```typescript
const handleFormSubmit = useCallback(async (values: AlertSend) => {
  setSubmitLoading(true);
  try {
    if (currentRecord) {
      await AlertSendService.updateAlertSend(values);
      
      if (isUnmountedRef.current) {
        console.log('组件已卸载，跳过更新成功后的状态更新');
        return;
      }
      
      message.success('更新成功');
    } else {
      await AlertSendService.addAlertSend(values);
      
      if (isUnmountedRef.current) {
        console.log('组件已卸载，跳过新增成功后的状态更新');
        return;
      }
      
      message.success('新增成功');
    }

    hideEditDrawer();
    editForm.resetFields();
    setCurrentRecord(null);
    await refreshData();
  } catch (error) {
    if (!isUnmountedRef.current) {
      message.error('提交失败');
    }
  } finally {
    if (!isUnmountedRef.current) {
      setSubmitLoading(false);
    }
  }
}, [currentRecord, message, hideEditDrawer, editForm, refreshData]);
```

### 4. 用户交互操作保护

#### 分页变化处理
```typescript
const handlePaginationChange = (page: number, pageSize: number) => {
  // 检查组件是否已卸载
  if (isUnmountedRef.current) {
    console.log('组件已卸载，跳过分页变化处理');
    return;
  }
  
  console.log('分页变化:', { page, pageSize });
  updatePagination(page, pageSize);
  loadData({
    current: page,
    page_size: pageSize,
  });
};
```

#### 搜索表单提交处理
```typescript
const handleSearchFormSubmit = useCallback((values: AlertSendSearchParams) => {
  // 检查组件是否已卸载
  if (isUnmountedRef.current) {
    console.log('组件已卸载，跳过搜索表单提交处理');
    return;
  }
  
  console.log('搜索参数:', values);
  updateSearchParams(values);
  updatePagination(1, pagination.page_size);
  loadData({
    ...values,
    current: 1,
    page_size: pagination.page_size,
  });
}, [updateSearchParams, updatePagination, pagination.page_size, loadData]);
```

#### 重置搜索处理
```typescript
const handleReset = useCallback(() => {
  // 检查组件是否已卸载
  if (isUnmountedRef.current) {
    console.log('组件已卸载，跳过重置搜索处理');
    return;
  }
  
  // 重置表单和其他状态
  searchForm.resetFields();
  resetSortAndFilter();
  clearSelection();
  
  // 重置搜索参数和分页状态
  updateSearchParams({});
  updatePagination(DEFAULT_PAGINATION.current, DEFAULT_PAGINATION.page_size);
  
  // 重新加载数据
  loadData({
    current: DEFAULT_PAGINATION.current,
    page_size: DEFAULT_PAGINATION.page_size,
    name: undefined,
    receive_type: undefined,
  });
}, [searchForm, resetSortAndFilter, clearSelection, updateSearchParams, updatePagination, loadData]);
```

## 清理的资源类型

1. **表单状态**：搜索表单和编辑表单的字段值
2. **选择状态**：跨页面选择的数据
3. **抽屉状态**：编辑抽屉的显示状态
4. **组件内部状态**：当前编辑记录、提交加载状态等
5. **异步操作**：防止在组件卸载后执行状态更新

## 最佳实践

1. **使用 useRef 标志**：通过 `isUnmountedRef` 标志防止在组件卸载后更新状态
2. **异步操作保护**：在所有异步操作的回调中检查卸载标志
3. **错误处理**：在清理过程中添加 try-catch 错误处理
4. **详细日志**：添加清理过程的日志记录，便于调试
5. **依赖数组**：在 useEffect 的依赖数组中包含所有使用的函数和状态

## 注意事项

1. **HTTP 请求取消**：由于使用 axios，无法直接取消请求，但通过卸载标志防止状态更新
2. **清理顺序**：按照逻辑顺序清理资源，避免依赖问题
3. **性能考虑**：卸载检查的性能开销很小，但能有效防止内存泄漏
4. **测试验证**：建议通过快速切换页面或组件来测试卸载逻辑的有效性

这种完整的卸载逻辑确保了组件在各种情况下都能正确清理，提高了应用的稳定性和性能。
