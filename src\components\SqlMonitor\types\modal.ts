/**
 * 模态框相关类型定义
 */

/**
 * Modal状态类型
 */
export type ModalState = {
  visible: boolean;
  loading?: boolean;
  title?: string;
  width?: number | string;
};

/**
 * 抽屉状态类型
 */
export type DrawerState = {
  visible: boolean;
  loading?: boolean;
  title?: string;
  width?: number | string;
  placement?: 'left' | 'right' | 'top' | 'bottom';
};

/**
 * 确认对话框配置类型
 */
export interface ConfirmConfig {
  title: string;
  content?: string;
  okText?: string;
  cancelText?: string;
  onOk?: () => void | Promise<void>;
  onCancel?: () => void;
  type?: 'info' | 'success' | 'error' | 'warning' | 'confirm';
}

/**
 * 通知配置类型
 */
export interface NotificationConfig {
  type: 'success' | 'info' | 'warning' | 'error';
  message: string;
  description?: string;
  duration?: number;
  placement?: 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';
}

/**
 * 消息配置类型
 */
export interface MessageConfig {
  type: 'success' | 'info' | 'warning' | 'error' | 'loading';
  content: string;
  duration?: number;
}

/**
 * 模态框组件属性类型
 */
export interface ModalProps {
  visible: boolean;
  title?: string;
  width?: number | string;
  maskClosable?: boolean;
  destroyOnClose?: boolean;
  onCancel?: () => void;
  onOk?: () => void;
  footer?: React.ReactNode;
  children?: React.ReactNode;
}

/**
 * 抽屉组件属性类型
 */
export interface DrawerProps {
  visible: boolean;
  title?: string;
  width?: number | string;
  placement?: 'left' | 'right' | 'top' | 'bottom';
  maskClosable?: boolean;
  destroyOnClose?: boolean;
  onClose?: () => void;
  footer?: React.ReactNode;
  children?: React.ReactNode;
}
