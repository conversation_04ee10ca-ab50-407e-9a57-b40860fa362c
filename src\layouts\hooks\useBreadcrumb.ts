import React, { useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { Space } from 'antd';
import { HomeOutlined } from '@ant-design/icons';
import type { BreadcrumbItem } from '../types';

/**
 * 面包屑导航 Hook
 */
export const useBreadcrumb = (): BreadcrumbItem[] => {
  const location = useLocation();

  const breadcrumbItems = useMemo(() => {
    const path = location.pathname;
    const items: BreadcrumbItem[] = [
      {
        href: '/dashboard',
        title: React.createElement(Space, {}, React.createElement(HomeOutlined), React.createElement('span', {}, '首页')),
      },
    ];

    if (path === '/users') {
      items.push({
        href: '/users',
        title: React.createElement('span', {}, '用户管理'),
      });
    } else if (path === '/tasks') {
      items.push({
        href: '/tasks',
        title: React.createElement('span', {}, '任务管理'),
      });
    } else if (path === '/teams') {
      items.push({
        href: '/teams',
        title: React.createElement('span', {}, '团队协作'),
      });
    }

    return items;
  }, [location.pathname]);

  return breadcrumbItems;
};
