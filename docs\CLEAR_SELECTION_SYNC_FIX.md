# 修复取消全选状态同步问题

## 问题描述

在选择发送功能中，当用户点击"取消全选"按钮时，AlertSendTable内部的选择状态被清空了，但是NotificationConfigTab中的`selectedRows`状态没有同步更新，导致：

1. 抽屉header中的"确认选择"按钮仍然显示选择数量
2. 按钮状态没有正确禁用
3. 父子组件状态不一致

## 问题原因

### 原始代码问题
```typescript
// AlertSendTable.tsx - 原始代码
<AlertSendActionButtons
  onClearSelection={clearSelection}  // 只清理了内部状态
  isSelectionMode={isSelectionMode}
/>
```

**问题分析:**
- `clearSelection()` 只清理了AlertSendTable内部的选择状态
- 没有通知父组件NotificationConfigTab更新`selectedRows`状态
- 导致父子组件状态不同步

## 解决方案

### 修复后的代码
```typescript
// AlertSendTable.tsx - 修复后
<AlertSendActionButtons
  onClearSelection={() => {
    clearSelection();
    // 在选择模式下，通知父组件选择已清空
    if (isSelectionMode && onSelectionConfirm) {
      onSelectionConfirm([]);
    }
  }}
  isSelectionMode={isSelectionMode}
/>
```

**解决方案说明:**
1. **保持原有功能**: 继续调用`clearSelection()`清理内部状态
2. **添加状态同步**: 在选择模式下通知父组件状态变化
3. **传递空数组**: 通过`onSelectionConfirm([])`告知父组件选择已清空
4. **条件判断**: 只在选择模式下执行同步逻辑

## 修复效果

### 1. 状态同步正确
- 点击"取消全选"后，`selectedRows`状态立即更新为空数组
- 父子组件状态保持一致

### 2. UI状态正确
- 抽屉header中的"确认选择"按钮正确禁用
- 按钮文本不再显示错误的选择数量
- 用户界面反馈准确

### 3. 用户体验改善
- 操作反馈及时准确
- 避免了用户困惑
- 交互逻辑更加直观

## 技术实现细节

### 1. 回调机制
```typescript
// 利用现有的onSelectionConfirm回调
if (isSelectionMode && onSelectionConfirm) {
  onSelectionConfirm([]); // 传递空数组表示清空选择
}
```

### 2. 条件判断
- `isSelectionMode`: 确保只在选择模式下执行
- `onSelectionConfirm`: 确保回调函数存在

### 3. 状态流向
```
用户点击"取消全选" 
  ↓
AlertSendTable.clearSelection() (清理内部状态)
  ↓
onSelectionConfirm([]) (通知父组件)
  ↓
NotificationConfigTab.setSelectedRows([]) (更新父组件状态)
  ↓
UI重新渲染 (按钮状态更新)
```

## 测试场景

### 1. 基本功能测试
1. 打开选择发送抽屉
2. 选择一些数据项
3. 点击"取消全选"按钮
4. 验证按钮状态是否正确禁用

### 2. 状态一致性测试
1. 选择数据后点击"取消全选"
2. 再次选择数据
3. 验证选择状态是否正确

### 3. 交互流程测试
1. 多次执行选择和取消全选操作
2. 验证每次操作后的状态是否正确
3. 确认没有状态残留问题

## 代码改动总结

**修改文件**: `src/components/SqlMonitor/components/AlertSendTable.tsx`

**修改内容**:
- 将`onClearSelection={clearSelection}`改为自定义函数
- 在自定义函数中添加状态同步逻辑
- 保持原有功能的同时增加父组件通知

**影响范围**:
- 只影响选择模式下的"取消全选"功能
- 不影响普通模式的使用
- 不影响其他组件的功能

## 总结

通过在`clearSelection`调用后添加父组件状态同步逻辑，成功解决了"取消全选"按钮的状态同步问题。这个修复：

- **保持了原有功能**的完整性
- **增强了状态同步**的可靠性  
- **改善了用户体验**
- **遵循了React状态管理**的最佳实践

修复后，选择发送功能的状态管理更加健壮和一致。
