import React, { useState, useEffect } from 'react';
import { Button, Space, Drawer, App } from 'antd';
import { ClearOutlined, DeleteOutlined, CopyOutlined, CheckOutlined, SendOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { AlertSend, KafkaConfig, PrometheusConfig } from '../../types';
import { formStyles } from '../../styles';
import AlertSendTable from '../AlertSendTable';

interface NotificationConfigTabProps {
  alertSends: AlertSend[];
  onAlertSendsChange: (alertSends: AlertSend[]) => void;
}

/**
 * 告警发送配置标签页组件
 * 管理告警发送方式的配置
 */
const NotificationConfigTab: React.FC<NotificationConfigTabProps> = ({ alertSends, onAlertSendsChange }) => {
  const { message } = App.useApp();

  // 动态高度状态
  const [containerHeight, setContainerHeight] = useState(600);

  // 抽屉状态管理
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRows, setSelectedRows] = useState<AlertSend[]>(alertSends);
  const [selectedRowsFromChild, setSelectedRowsFromChild] = useState<AlertSend[]>([]);

  // 计算动态高度
  useEffect(() => {
    const calculateHeight = () => {
      // 100vh - (60+84+46+57) = 100vh - 247px
      const dynamicHeight = window.innerHeight - 247;
      setContainerHeight(Math.max(400, dynamicHeight)); // 最小高度400px
    };

    // 初次加载时计算
    calculateHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', calculateHeight);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, []);

  // 删除告警发送配置
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = selectedRows.filter((_, i) => i !== index);
    setSelectedRows(newAlertSends);
  };

  // 清空所有选择
  const handleClearAll = () => {
    setSelectedRows([]);
    message.success('已清空所有选择');
  };

  // 处理选择发送按钮点击
  const handleSelectAlertSend = () => {
    setDrawerVisible(true);
  };

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setDrawerVisible(false);
  };

  // 处理抽屉内的选择确认
  const handleDrawerSelectionConfirm = () => {
    console.log('确认选择时的selectedRows:', selectedRowsFromChild);

    setSelectedRows(selectedRowsFromChild);

    onAlertSendsChange(selectedRows);

    handleDrawerClose();
    message.success(`已选择 ${selectedRowsFromChild.length} 个告警发送配置`);
  };

  // 处理选择变化（从AlertSendTable传递过来）
  const handleSelectionChange = (selectedAlertSends: AlertSend[]) => {
    console.log('NotificationConfigTab 接收到选择变化:', selectedAlertSends);
    // 接收子组件传递的选择数据，保存为副本，
    // 当点击确认按钮时在更新当前页面数据
    // 因为当前页面数据会直接传递给子组件
    // 这样避免了无限循环渲染
    setSelectedRowsFromChild(selectedAlertSends);
  };

  // 复制到剪贴板的函数
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success(`${label}已复制到剪贴板`);
    } catch {
      message.error('复制失败');
    }
  };

  // 可复制字段组件
  const CopyableField: React.FC<{
    label: string;
    value: string;
    canCopy?: boolean;
  }> = ({ label, value, canCopy = true }) => {
    return (
      <div>
        <div style={{ fontSize: '11px', color: '#8c8c8c', marginBottom: '4px', textTransform: 'uppercase', letterSpacing: '0.5px' }}>{label}</div>
        <div
          style={{
            position: 'relative',
            padding: '6px 10px',
            backgroundColor: canCopy ? '#f5f5f5' : label === '类型' ? (value.toLowerCase() === 'kafka' ? '#e6f7ff' : '#f6ffed') : '#f5f5f5',
            color: canCopy ? '#595959' : label === '类型' ? (value.toLowerCase() === 'kafka' ? '#1890ff' : '#52c41a') : '#595959',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: canCopy ? 'normal' : '600',
            textAlign: canCopy ? 'left' : 'center',
            fontFamily: canCopy ? 'monospace' : 'inherit',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
            cursor: canCopy ? 'pointer' : 'default',
            textTransform: canCopy ? 'none' : 'uppercase',
            transition: 'all 0.2s ease',
          }}
          title={value} // 悬停显示完整信息
          onClick={canCopy ? () => copyToClipboard(value, label) : undefined}
          onMouseEnter={
            canCopy
              ? e => {
                  e.currentTarget.style.backgroundColor = '#e6f7ff';
                  e.currentTarget.style.borderColor = '#91d5ff';
                }
              : undefined
          }
          onMouseLeave={
            canCopy
              ? e => {
                  e.currentTarget.style.backgroundColor = '#f5f5f5';
                  e.currentTarget.style.borderColor = 'transparent';
                }
              : undefined
          }
        >
          {value}
          {canCopy && (
            <CopyOutlined
              style={{
                position: 'absolute',
                right: '8px',
                top: '50%',
                transform: 'translateY(-50%)',
                opacity: 0.5,
                fontSize: '10px',
              }}
            />
          )}
        </div>
      </div>
    );
  };

  const renderAlertSendItem = (alertSend: AlertSend, index: number) => {
    // 解析配置信息
    let parsedConfig: KafkaConfig | PrometheusConfig | null = null;
    try {
      parsedConfig = typeof alertSend.config === 'string' ? JSON.parse(alertSend.config) : alertSend.config;
    } catch (error) {
      console.error('解析配置信息失败:', error);
      parsedConfig = null;
    }

    return (
      <div
        key={alertSend.id}
        style={{
          backgroundColor: '#fff',
          border: '1px solid #e8e8e8',
          borderRadius: '8px',
          padding: '20px',
          position: 'relative',
          transition: 'all 0.2s ease',
          boxShadow: '0 1px 3px rgba(0,0,0,0.1)',
        }}
        onMouseEnter={e => {
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
          e.currentTarget.style.transform = 'translateY(-2px)';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.boxShadow = '0 1px 3px rgba(0,0,0,0.1)';
          e.currentTarget.style.transform = 'translateY(0)';
        }}
      >
        {/* 操作按钮 - 右上角 */}
        <div style={{ position: 'absolute', top: '12px', right: '12px' }}>
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteAlertSend(index)}
            style={{
              width: '24px',
              height: '24px',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: 0,
            }}
            title="取消选择"
          ></Button>
        </div>

        {/* 名称和ID */}
        <div style={{ marginBottom: '16px', paddingRight: '40px' }}>
          <h3
            style={{
              margin: 0,
              fontSize: '18px',
              fontWeight: '600',
              color: '#262626',
              lineHeight: '1.4',
              marginBottom: '4px',
            }}
          >
            {alertSend.name}
          </h3>
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>ID: {alertSend.id}</div>
        </div>

        {/* 字段网格 - 根据类型动态调整 */}
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: alertSend.receive_type === 'kafka' ? 'repeat(2, 1fr)' : '1fr',
            gap: '12px',
            marginBottom: '16px',
          }}
        >
          {/* 发送类型 - 不可复制 */}
          <CopyableField label="类型" value={alertSend.receive_type} canCopy={false} />

          {/* Topic - 仅 Kafka 类型显示 */}
          {alertSend.receive_type === 'kafka' && parsedConfig && <CopyableField label="Topic" value={parsedConfig.topic || '未设置'} canCopy={true} />}
        </div>

        {/* 连接地址信息 - 单独一行，可复制 */}
        {parsedConfig && parsedConfig.address && (
          <div style={{ marginBottom: '12px' }}>
            <CopyableField label="连接地址" value={parsedConfig.address} canCopy={true} />
          </div>
        )}

        {/* 认证信息 - 仅在有用户名时显示 */}
        {parsedConfig && parsedConfig.username && (
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(2, 1fr)',
              gap: '12px',
              marginBottom: '12px',
            }}
          >
            <CopyableField label="用户名" value={parsedConfig.username} canCopy={true} />
            <CopyableField label="密码" value={parsedConfig.password ? '••••••••' : '未设置'} canCopy={false} />
          </div>
        )}

        {/* 时间信息 */}
        <div
          style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(2, 1fr)',
            gap: '12px',
            marginTop: '16px',
            paddingTop: '12px',
            borderTop: '1px solid #f0f0f0',
          }}
        >
          <div>
            <div style={{ fontSize: '11px', color: '#8c8c8c', marginBottom: '4px', textTransform: 'uppercase', letterSpacing: '0.5px' }}>创建时间</div>
            <div style={{ fontSize: '12px', color: '#595959' }}>{alertSend.create_time}</div>
          </div>
          <div>
            <div style={{ fontSize: '11px', color: '#8c8c8c', marginBottom: '4px', textTransform: 'uppercase', letterSpacing: '0.5px' }}>更新时间</div>
            <div style={{ fontSize: '12px', color: '#595959' }}>{alertSend.update_time}</div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      className={formStyles.tabContent}
      style={{
        position: 'relative',
        height: `${containerHeight}px`, // 动态高度
        display: 'flex',
        flexDirection: 'column',
        border: '1px solid #e8e8e8',
        borderRadius: '8px',
        backgroundColor: '#fff',
      }}
    >
      {/* 固定顶部 - 标题和操作按钮 */}
      <div
        style={{
          position: 'sticky',
          top: 0,
          zIndex: 10,
          backgroundColor: '#fff',
          borderBottom: '1px solid #e8e8e8',
          padding: '16px 20px',
          borderRadius: '8px 8px 0 0',
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
            <h3 style={{ margin: 0, fontSize: '16px', fontWeight: '600', color: '#262626' }}>告警发送配置</h3>
            <span style={{ fontSize: '14px', color: '#8c8c8c' }}>共 {selectedRows.length} 个配置</span>
          </div>
          <Space>
            {selectedRows.length > 0 && (
              <Button danger size="small" type="text" icon={<ClearOutlined />} onClick={handleClearAll}>
                清空选择
              </Button>
            )}
            <Button size="small" type="text" icon={<SendOutlined />} onClick={handleSelectAlertSend}>
              选择发送
            </Button>
          </Space>
        </div>
      </div>

      {/* 可滚动的中间内容区域 */}
      <div
        style={{
          flex: 1,
          overflowY: 'auto',
          overflowX: 'hidden',
          padding: '16px 20px',
          backgroundColor: '#f8f9fa',
          maxHeight: `calc(${containerHeight}px - 70px)`, // 减去顶部的高度
        }}
      >
        {selectedRows.length === 0 ? (
          <div
            style={{
              textAlign: 'center',
              padding: '60px 20px',
              color: '#bfbfbf',
              backgroundColor: '#fafafa',
              borderRadius: '8px',
              border: '1px dashed #d9d9d9',
            }}
          >
            <div style={{ fontSize: '16px', marginBottom: '8px' }}>暂无告警发送配置</div>
            <div style={{ fontSize: '14px' }}>请点击"新增发送"或"选择发送"添加配置</div>
          </div>
        ) : (
          <div
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(3, 1fr)', // 每行固定三个卡片
              gap: '16px',
              paddingBottom: '10px', // 底部留白，确保最后一项不被遮挡
            }}
          >
            {selectedRows.map((alertSend, index) => renderAlertSendItem(alertSend, index))}
          </div>
        )}
      </div>

      {/* 选择发送抽屉 */}
      <Drawer
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
            <span>选择告警发送配置</span>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleDrawerSelectionConfirm}
              // disabled={selectedRows.length === 0}
            >
              确认选择 {selectedRowsFromChild.length > 0 && `(${selectedRowsFromChild.length})`}
            </Button>
          </div>
        }
        width="80%"
        open={drawerVisible}
        onClose={handleDrawerClose}
        maskClosable={false}
      >
        <AlertSendTable contentHeight={800} isSelectionMode={true} selectedRows={selectedRows} onSelectionConfirm={handleSelectionChange} />
      </Drawer>
    </div>
  );
};

export default NotificationConfigTab;
