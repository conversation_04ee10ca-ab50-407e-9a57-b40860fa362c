import React, { useState, useCallback, useRef, useEffect } from 'react';
import { ListTable } from '@visactor/react-vtable';
import { Button, Space, App } from 'antd';

interface TableData {
  id: number;
  name: string;
  age: number;
  email: string;
  department: string;
  selected?: boolean; // 添加选中状态
}

const ReactVTableExample: React.FC = () => {
  const { message } = App.useApp();
  // 表格数据状态
  const [tableData, setTableData] = useState<TableData[]>([]);

  // 多选状态管理
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);

  // 表格引用
  const tableRef = useRef<typeof ListTable | null>(null);

  // 容器尺寸状态
  const [containerSize, setContainerSize] = useState({
    width: 0,
    height: 0,
  });
  const containerRef = useRef<HTMLDivElement>(null);

  // 移除不再需要的indeterminate状态变量，直接使用函数计算

  // 生成10000条模拟数据
  const generateMockData = useCallback((): TableData[] => {
    const departments = ['技术部', '市场部', '人事部', '财务部', '运营部', '设计部', '产品部', '销售部'];
    const firstNames = ['张', '李', '王', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林'];
    const secondNames = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '涛', '明', '超', '秀兰'];

    console.log('开始生成10000条数据...');
    const startTime = performance.now();

    const data = Array.from({ length: 10000 }, (_, index) => ({
      id: index + 1,
      name: `${firstNames[Math.floor(Math.random() * firstNames.length)]}${secondNames[Math.floor(Math.random() * secondNames.length)]}`,
      age: 18 + Math.floor(Math.random() * 47), // 18-65岁
      email: `user${(index + 1).toString().padStart(5, '0')}@company.com`,
      department: departments[Math.floor(Math.random() * departments.length)],
      selected: false,
    }));

    const endTime = performance.now();
    console.log(`数据生成完成，耗时: ${(endTime - startTime).toFixed(2)}ms`);

    return data;
  }, []);

  // 初始化数据
  useEffect(() => {
    const initialData = generateMockData();
    setTableData(initialData);
  }, [generateMockData]);

  // 自适应容器尺寸
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const { clientWidth, clientHeight } = containerRef.current;
        setContainerSize({
          width: clientWidth,
          height: clientHeight,
        });
      }
    };

    // 初始化尺寸
    updateSize();

    // 监听窗口大小变化
    window.addEventListener('resize', updateSize);

    return () => {
      window.removeEventListener('resize', updateSize);
    };
  }, []);

  // 多选处理函数
  const handleRowSelect = useCallback((rowId: number, checked: boolean) => {
    setTableData(prevData =>
      prevData.map(item =>
        item.id === rowId
          ? {
              ...item,
              selected: checked,
            }
          : item
      )
    );

    setSelectedRowKeys(prevKeys => {
      if (checked) {
        // 添加id（避免重复添加）
        return prevKeys.includes(rowId) ? prevKeys : [...prevKeys, rowId];
      } else {
        // 移除id
        return prevKeys.filter(key => key !== rowId);
      }
    });
  }, []);

  // 全选/取消全选
  const handleSelectAll = useCallback((checked: boolean) => {
    setTableData(prevData => {
      const updatedData = prevData.map(item => ({
        ...item,
        selected: checked,
      }));

      // 同步更新选中的行键
      if (checked) {
        setSelectedRowKeys(updatedData.map(item => item.id));
      } else {
        setSelectedRowKeys([]);
      }

      return updatedData;
    });
  }, []);

  // 清除选择
  const clearSelection = useCallback(() => {
    setTableData(prevData =>
      prevData.map(item => ({
        ...item,
        selected: false,
      }))
    );
    setSelectedRowKeys([]);
    message.info('已清除所有选择');
  }, []);

  // 批量删除
  const handleBatchDelete = useCallback(() => {
    const selectedCount = selectedRowKeys.length;
    if (selectedCount === 0) {
      message.warning('请先选择要删除的行');
      return;
    }

    // 从数据中移除选中的行
    setTableData(prevData => prevData.filter(item => !selectedRowKeys.includes(item.id)));

    setSelectedRowKeys([]);
    message.success(`已删除 ${selectedCount} 条记录`);
  }, [selectedRowKeys]);

  // 确保selectedRowKeys与实际存在的数据保持同步
  useEffect(() => {
    const existingIds = tableData.map(item => item.id);
    const validSelectedKeys = selectedRowKeys.filter(key => existingIds.includes(key));

    // 如果有无效的选中项，清理它们
    if (validSelectedKeys.length !== selectedRowKeys.length) {
      setSelectedRowKeys(validSelectedKeys);
    }
  }, [tableData, selectedRowKeys]);

  // 使用VTable原生checkbox，不需要手动添加checkbox字段
  // tableData已经包含selected字段，直接使用

  // 使用VTable原生的列宽配置，支持百分比和auto
  // console.log("VTable配置:", {
  //   widthMode: "adaptive",
  //   autoFillWidth: true,
  //   containerWidth: containerSize.width,
  //   tableWidth: containerSize.width - 32,
  //   columnsConfig: "百分比宽度 + minWidth限制",
  // });

  // 计算表头checkbox状态 - 实现联动
  const getHeaderCheckboxState = useCallback(() => {
    const totalCount = tableData.length;
    const selectedCount = selectedRowKeys.length;

    if (totalCount === 0) return false; // 无数据时
    return selectedCount === totalCount; // 全选时为true，否则为false
  }, [tableData.length, selectedRowKeys.length]);

  // 获取表头checkbox的indeterminate状态
  const getHeaderIndeterminate = useCallback(() => {
    const totalCount = tableData.length;
    const selectedCount = selectedRowKeys.length;

    return selectedCount > 0 && selectedCount < totalCount;
  }, [tableData.length, selectedRowKeys.length]);

  // 监听选中状态变化，输出调试信息
  useEffect(() => {
    const totalCount = tableData.length;
    const selectedCount = selectedRowKeys.length;
    const newIndeterminate = selectedCount > 0 && selectedCount < totalCount;

    console.log('状态更新:', {
      totalCount,
      selectedCount,
      indeterminate: newIndeterminate,
      checked: selectedCount === totalCount && totalCount > 0,
    });
  }, [selectedRowKeys.length, tableData.length]);

  // 列配置 - 使用VTable原生checkbox功能
  const columns = [
    {
      field: 'selected',
      // title: "选择",
      cellType: 'checkbox' as const, // 使用VTable原生checkbox
      headerType: 'checkbox' as const, // 表头也使用原生checkbox
      width: '4%', // 使用百分比宽度
      minWidth: 40,
      // 动态计算表头checkbox状态，实现联动
      checked: getHeaderCheckboxState(), // 使用函数动态计算
      // VTable原生支持indeterminate状态 - 使用函数动态计算
      indeterminate: getHeaderIndeterminate(),
      headerStyle: {
        textAlign: 'center' as const,
        fontWeight: 600,
        color: '#374151',
      },
      style: {
        textAlign: 'center' as const,
        fontWeight: 500,
        color: '#6b7280',
      },
    },
    {
      field: 'id',
      title: 'ID',
      width: '10%',
      minWidth: 80,
      sort: true,
      headerStyle: {
        textAlign: 'center' as const,
        fontWeight: 600,
        color: '#374151',
      },
      style: {
        textAlign: 'center' as const,
        fontWeight: 500,
        color: '#6b7280',
      },
    },
    {
      field: 'name',
      title: '姓名',
      width: '15%',
      minWidth: 100,
      sort: true,
      headerStyle: {
        textAlign: 'left' as const,
        fontWeight: 600,
        color: '#374151',
      },
      style: {
        textAlign: 'left' as const,
        fontWeight: 500,
        color: '#111827',
      },
    },
    {
      field: 'age',
      title: '年龄',
      width: '10%',
      minWidth: 80,
      sort: true,
      headerStyle: {
        textAlign: 'center' as const,
        fontWeight: 600,
        color: '#374151',
      },
      style: {
        textAlign: 'center' as const,
        fontWeight: 500,
        color: '#6b7280',
      },
    },
    {
      field: 'email',
      title: '邮箱',
      width: '35%',
      minWidth: 200,
      sort: true,
      headerStyle: {
        textAlign: 'left' as const,
        fontWeight: 600,
        color: '#374151',
      },
      style: {
        textAlign: 'left' as const,
        fontWeight: 400,
        color: '#4f46e5',
      },
    },
    {
      field: 'department',
      title: '部门',
      width: '22%',
      minWidth: 100,
      sort: true,
      headerStyle: {
        textAlign: 'left' as const,
        fontWeight: 600,
        color: '#374151',
      },
      style: {
        textAlign: 'left' as const,
        fontWeight: 400,
        color: '#374151',
      },
    },
  ];

  return (
    <div
      ref={containerRef}
      style={{
        height: '600px',
        width: '100%',
        padding: '24px',
        backgroundColor: '#f8fafc', // 现代化背景色
        fontFamily: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif",
      }}
    >
      <div
        style={{
          marginBottom: '24px',
        }}
      >
        <h1
          style={{
            margin: '0 0 16px 0',
            fontSize: '32px',
            fontWeight: 700,
            color: '#111827',
            letterSpacing: '-0.025em',
          }}
        >
          VTable 高性能表格
        </h1>

        {/* 功能特性标签 */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            flexWrap: 'wrap',
            marginBottom: '16px',
          }}
        >
          <div
            style={{
              padding: '6px 12px',
              backgroundColor: '#dbeafe',
              borderRadius: '20px',
              fontSize: '13px',
              fontWeight: 500,
              color: '#1e40af',
            }}
          >
            📊 10,000 条数据
          </div>
          <div
            style={{
              padding: '6px 12px',
              backgroundColor: selectedRowKeys.length > 0 ? '#dcfce7' : '#f3f4f6',
              borderRadius: '20px',
              fontSize: '13px',
              fontWeight: 500,
              color: selectedRowKeys.length > 0 ? '#166534' : '#6b7280',
            }}
          >
            ✅ 已选择 {selectedRowKeys.length} 项
          </div>
          <div
            style={{
              padding: '6px 12px',
              backgroundColor: getHeaderIndeterminate() ? '#fef3c7' : getHeaderCheckboxState() ? '#dcfce7' : '#f3f4f6',
              borderRadius: '20px',
              fontSize: '13px',
              fontWeight: 500,
              color: getHeaderIndeterminate() ? '#92400e' : getHeaderCheckboxState() ? '#166534' : '#6b7280',
            }}
          >
            {getHeaderIndeterminate() ? '⊟ 部分选中' : getHeaderCheckboxState() ? '☑ 全选' : '☐ 未选'}
          </div>
          <div
            style={{
              padding: '6px 12px',
              backgroundColor: '#fef3c7',
              borderRadius: '20px',
              fontSize: '13px',
              fontWeight: 500,
              color: '#92400e',
            }}
          >
            🚀 Canvas渲染
          </div>
          <div
            style={{
              padding: '6px 12px',
              backgroundColor: '#f3e8ff',
              borderRadius: '20px',
              fontSize: '13px',
              fontWeight: 500,
              color: '#7c3aed',
            }}
          >
            ⚡ 虚拟滚动
          </div>
        </div>

        {/* 功能描述 */}
        <p
          style={{
            margin: '0',
            color: '#6b7280',
            fontSize: '14px',
            lineHeight: '1.5',
          }}
        >
          ✨ 自适应大小 • 多选功能 • 排序功能 • 点击选择 • 表头级联选择 • Checkbox级联
        </p>

        {/* 多选控制区域 */}
        <div
          style={{
            marginBottom: '20px',
            padding: '20px',
            backgroundColor: '#ffffff',
            borderRadius: '12px',
            border: '1px solid #e5e7eb',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
          }}
        >
          <div
            style={{
              marginBottom: '16px',
              fontSize: '16px',
              fontWeight: 600,
              color: '#374151',
              display: 'flex',
              alignItems: 'center',
              gap: '8px',
            }}
          >
            🎛️ 操作控制
            {selectedRowKeys.length > 0 && (
              <span
                style={{
                  padding: '4px 8px',
                  backgroundColor: '#dcfce7',
                  color: '#166534',
                  borderRadius: '12px',
                  fontSize: '12px',
                  fontWeight: 500,
                }}
              >
                {selectedRowKeys.length} 项已选
              </span>
            )}
            <span
              style={{
                padding: '4px 8px',
                backgroundColor: '#f0f9ff',
                color: '#0369a1',
                borderRadius: '12px',
                fontSize: '12px',
                fontWeight: 500,
              }}
            >
              🔗 级联选择已启用
            </span>
          </div>
          <Space size="middle" wrap>
            <Button
              size="middle"
              type="primary"
              onClick={() => handleSelectAll(true)}
              disabled={selectedRowKeys.length === tableData.length}
              style={{
                borderRadius: '8px',
                fontWeight: 500,
                boxShadow: '0 2px 4px rgba(24, 144, 255, 0.2)',
              }}
            >
              ✅ 全选
            </Button>
            <Button
              size="middle"
              onClick={() => handleSelectAll(false)}
              disabled={selectedRowKeys.length === 0}
              style={{
                borderRadius: '8px',
                fontWeight: 500,
              }}
            >
              ❌ 取消全选
            </Button>
            <Button
              size="middle"
              type="dashed"
              onClick={() => {
                const newData = generateMockData();
                setTableData(newData);
                setSelectedRowKeys([]);
                message.success('重新生成10000条数据完成！');
              }}
              style={{
                borderRadius: '8px',
                fontWeight: 500,
                borderColor: '#faad14',
                color: '#faad14',
              }}
            >
              🔄 重新生成数据
            </Button>
            {selectedRowKeys.length > 0 && (
              <>
                <Button
                  size="middle"
                  onClick={clearSelection}
                  style={{
                    borderRadius: '8px',
                    fontWeight: 500,
                    borderColor: '#d9d9d9',
                  }}
                >
                  🧹 清除选择
                </Button>
                <Button
                  size="middle"
                  type="primary"
                  danger
                  onClick={handleBatchDelete}
                  style={{
                    borderRadius: '8px',
                    fontWeight: 500,
                    boxShadow: '0 2px 4px rgba(255, 77, 79, 0.2)',
                  }}
                >
                  🗑️ 批量删除 ({selectedRowKeys.length})
                </Button>
              </>
            )}
          </Space>
        </div>
      </div>

      <div
        style={{
          backgroundColor: '#ffffff',
          borderRadius: '16px',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
          border: '1px solid #e5e7eb',
          overflow: 'hidden', // 确保圆角效果
          height: containerSize.height - 200, // 为标题和控制区域留出空间
          width: '100%',
        }}
      >
        <ListTable
          ref={tableRef}
          records={tableData}
          columns={columns}
          width={containerSize.width - 32 || 800} // 表格占满容器宽度，减去padding
          height={containerSize.height - 120 || 400} // 自适应高度，带默认值
          widthMode="adaptive" // 自适应容器宽度模式，列宽按比例分配
          autoFillWidth={true} // 自动填充宽度，确保列宽铺满100%
          defaultRowHeight={40} // 默认行高
          defaultHeaderRowHeight={45} // 表头行高
          limitMinWidth={30} // 限制最小列宽
          animationAppear={false} // 关闭出现动画，提升大数据性能
          // 启用表头checkbox级联选择功能
          enableHeaderCheckboxCascade={true} // 启用表头checkbox级联选择
          enableCheckboxCascade={true} // 启用checkbox级联选择（默认为true）
          // 主题配置 - 现代化表格样式
          theme={{
            headerStyle: {
              bgColor: '#f8fafc', // 现代化表头背景
              color: '#374151', // 深灰色文字
              fontSize: 14, // 统一字体大小
              fontWeight: 600, // 加粗表头
              textAlign: 'center' as const, // 统一居中对齐
              borderColor: '#e5e7eb', // 浅灰色边框
              borderLineWidth: 1,
            },
            bodyStyle: {
              bgColor: (args: { row: number; col: number }) => {
                const dataRowIndex = args.row - 1;
                if (dataRowIndex < 0) return '#ffffff';

                const record = tableData[dataRowIndex];

                // 选中行的特殊样式
                if (record?.selected) {
                  return args.col === 0 ? '#dbeafe' : '#eff6ff'; // 选中行蓝色渐变
                }

                // 斑马纹效果
                return dataRowIndex % 2 === 0 ? '#ffffff' : '#f9fafb';
              },
              color: (args: { row: number; col: number }) => {
                const dataRowIndex = args.row - 1;
                if (dataRowIndex < 0) return '#374151';

                const record = tableData[dataRowIndex];
                return record?.selected ? '#1e40af' : '#374151'; // 选中行深蓝色文字
              },
              fontSize: 13, // 稍小的正文字体
              textAlign: 'left' as const, // 统一左对齐
              borderColor: '#f3f4f6', // 更浅的边框
              borderLineWidth: 1,
            },
            // 整体表格样式
            frameStyle: {
              borderColor: '#e5e7eb',
              borderLineWidth: 1,
              cornerRadius: 8, // 圆角
              shadowBlur: 4, // 阴影
              shadowColor: 'rgba(0, 0, 0, 0.1)',
            },
          }}
          // 使用VTable原生的checkbox状态变化事件
          onCheckboxStateChange={(args: { row: number; col: number; checked: boolean }) => {
            console.log('Checkbox状态变化事件:', args);
            const { row, checked } = args;
            const dataRowIndex = row - 1; // VTable的行索引包含表头，需要减1

            if (dataRowIndex >= 0) {
              const record = tableData[dataRowIndex];
              if (record) {
                console.log('选择的记录:', record.name, '新状态:', checked);

                // 更新选中状态
                handleRowSelect(record.id, checked);
              }
            } else if (row === 0) {
              // 表头checkbox点击，智能全选/全反选
              const isAllSelected = getHeaderCheckboxState();
              const isIndeterminate = getHeaderIndeterminate();

              // 部分选中或未选中时，执行全选；全选时，执行取消全选
              const shouldSelectAll = !isAllSelected;

              console.log('表头checkbox点击 - 全选状态:', isAllSelected, '部分选中:', isIndeterminate, '执行操作:', shouldSelectAll ? '全选' : '取消全选');

              handleSelectAll(shouldSelectAll);
            }
          }}
          // 添加单元格点击事件，支持点击checkbox单元格选择
          onClickCell={(args: { row: number; col: number }) => {
            console.log('单元格点击:', args);

            // 只处理checkbox列的点击
            if (args.col === 0) {
              // 去除表头index
              const dataRowIndex = args.row - 1;

              if (dataRowIndex >= 0) {
                // 数据行checkbox点击
                const record = tableData[dataRowIndex];
                if (record) {
                  const newChecked = !record.selected;
                  console.log('点击checkbox单元格，切换状态:', record.name, '→', newChecked);
                  handleRowSelect(record.id, newChecked);
                }
              } else if (args.row === 0) {
                // 表头checkbox点击，智能全选/全反选
                const isAllSelected = getHeaderCheckboxState();
                const isIndeterminate = getHeaderIndeterminate();

                // 部分选中或未选中时，执行全选；全选时，执行取消全选
                const shouldSelectAll = !isAllSelected;

                console.log('表头checkbox点击(单元格) - 全选状态:', isAllSelected, '部分选中:', isIndeterminate, '执行操作:', shouldSelectAll ? '全选' : '取消全选');

                handleSelectAll(shouldSelectAll);
              }
            }
          }}
        />
      </div>
    </div>
  );
};

export default ReactVTableExample;
