# 告警表格重置功能修复

## 问题描述

在告警管理表格中，快速搜索表单区域的 `handleReset` 函数第一次调用时不会完全重置搜索表单的参数。虽然表单字段会被重置（视觉上清空），但实际的搜索参数状态可能仍然保留之前的值，导致数据加载时使用了旧的搜索条件。

## 问题原因

### 根本原因
React 状态更新是异步的。在 `handleReset` 函数中：

1. `resetData()` 调用会重置搜索参数状态为空对象 `{}`
2. 但是 `loadData()` 函数立即被调用，此时状态可能还没有更新
3. `loadData` 函数内部使用 `...searchParams` 来合并参数，如果状态还未更新，就会使用旧的搜索参数

### 代码分析

**修复前的代码：**
```typescript
const handleReset = useCallback(() => {
  searchForm.resetFields();
  resetData();                    // 异步更新状态
  resetSortAndFilter();
  clearSelection();
  
  // 立即调用 loadData，可能使用未更新的 searchParams
  loadDataRef.current({ 
    current: 1, 
    page_size: DEFAULT_PAGINATION.page_size 
  });
}, [resetData, resetSortAndFilter, clearSelection, searchForm]);
```

**loadData 函数中的参数合并：**
```typescript
const finalParams = {
  ...searchParams,  // 可能还是旧值
  ...pagination,
  ...customParams,  // 只包含分页参数
};
```

## 解决方案

### 修复方法
在 `loadData` 调用时明确传递所有搜索参数为 `undefined`，确保覆盖可能还未更新的状态值。

**修复后的代码：**
```typescript
const handleReset = useCallback(() => {
  searchForm.resetFields();
  resetData();
  resetSortAndFilter();
  clearSelection();

  // 明确传递空的搜索参数，覆盖可能还未更新的状态
  loadDataRef.current({
    current: 1,
    page_size: DEFAULT_PAGINATION.page_size,
    // 明确传递空的搜索参数
    name: undefined,
    severity: undefined,
    type: undefined,
    sql: undefined,
  });
}, [resetData, resetSortAndFilter, clearSelection, searchForm]);
```

### 修复原理
1. **参数优先级**：在 `loadData` 函数中，`customParams` 具有最高优先级，会覆盖 `searchParams` 中的同名字段
2. **明确重置**：通过明确传递 `undefined` 值，确保所有搜索字段都被重置
3. **状态一致性**：避免依赖异步状态更新，确保重置行为的一致性

## 测试验证

### 测试用例
1. **第一次重置测试**：验证第一次点击重置按钮时，搜索参数被正确清空
2. **连续重置测试**：验证多次重置操作的一致性
3. **重置后搜索测试**：验证重置后新的搜索功能正常工作

### 预期结果
- ✅ 第一次重置时搜索参数立即生效
- ✅ 表单字段和数据加载都正确重置
- ✅ 重置后的搜索功能正常工作

## 相关文件

- `src/components/SqlMonitor/components/AlertTable.tsx` - 主要修复文件
- `src/components/SqlMonitor/hooks/useAlertData.ts` - 数据管理 Hook
- `src/components/SqlMonitor/types/task.ts` - 类型定义
- `src/components/SqlMonitor/components/__tests__/AlertTable.reset.test.tsx` - 测试文件

## 注意事项

1. **类型安全**：确保传递的 `undefined` 值符合 `TaskAlertSearchParams` 类型定义
2. **完整性**：包含所有可能的搜索字段，避免遗漏
3. **一致性**：与搜索表单中的字段保持一致

## 扩展应用

这个修复方法可以应用到其他类似的表格组件中，如：
- 任务管理表格
- 数据库连接表格  
- 告警发送配置表格
- 其他信息配置表格

所有使用类似模式的组件都应该采用相同的修复方法，确保重置功能的可靠性。
