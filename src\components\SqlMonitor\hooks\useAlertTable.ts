/**
 * 告警表格管理Hook
 * 管理表格的排序、筛选、分页等状态
 */

import { useState, useCallback, useEffect } from 'react';
import type { TableProps } from 'antd';
import type { SorterResult, FilterValue, TablePaginationConfig } from 'antd/es/table/interface';
import type { TaskAlert } from '../types';
import { TABLE_SCROLL_CONFIG } from '../constants';

export interface UseAlertTableOptions {
  /** 内容高度（用于计算表格滚动高度） */
  contentHeight?: number;
  /** 是否启用排序 */
  enableSort?: boolean;
  /** 是否启用筛选 */
  enableFilter?: boolean;
}

export interface UseAlertTableReturn {
  /** 表格滚动高度 */
  tableScrollY: number;
  /** 排序状态 */
  sortedInfo: SorterResult<TaskAlert> | SorterResult<TaskAlert>[];
  /** 筛选状态 */
  filteredInfo: Record<string, FilterValue | null>;
  /** 表格变化处理函数 */
  handleTableChange: TableProps<TaskAlert>['onChange'];
  /** 获取排序信息的辅助函数 */
  getSortOrder: (field: string) => 'ascend' | 'descend' | null;
  /** 重置排序和筛选 */
  resetSortAndFilter: () => void;
  /** 设置表格滚动高度 */
  setTableScrollY: (height: number) => void;
}

/**
 * 告警表格管理Hook
 */
export function useAlertTable(options: UseAlertTableOptions = {}): UseAlertTableReturn {
  const { contentHeight, enableSort = true, enableFilter = true } = options;

  // 表格滚动高度状态
  const [tableScrollY, setTableScrollY] = useState<number>(TABLE_SCROLL_CONFIG.defaultY);

  // 排序状态
  const [sortedInfo, setSortedInfo] = useState<SorterResult<TaskAlert> | SorterResult<TaskAlert>[]>({});

  // 筛选状态
  const [filteredInfo, setFilteredInfo] = useState<Record<string, FilterValue | null>>({});

  // 表格变化处理函数
  const handleTableChange: TableProps<TaskAlert>['onChange'] = useCallback(
    (pagination: TablePaginationConfig, filters, sorter) => {
      console.log('表格状态变化:', { pagination, filters, sorter });

      // 更新排序状态
      if (enableSort) {
        setSortedInfo(sorter as SorterResult<TaskAlert> | SorterResult<TaskAlert>[]);
      }

      // 更新筛选状态
      if (enableFilter) {
        setFilteredInfo(filters);
      }
    },
    [enableSort, enableFilter]
  );

  // 获取排序信息的辅助函数
  const getSortOrder = useCallback(
    (field: string): 'ascend' | 'descend' | null => {
      if (Array.isArray(sortedInfo)) {
        const sortInfo = sortedInfo.find(sort => sort.field === field);
        return sortInfo?.order || null;
      }

      if (sortedInfo && sortedInfo.field === field) {
        return sortedInfo.order || null;
      }

      return null;
    },
    [sortedInfo]
  );

  // 重置排序和筛选
  const resetSortAndFilter = useCallback(() => {
    setSortedInfo({});
    setFilteredInfo({});
  }, []);

  // 根据传入的contentHeight计算表格滚动高度
  useEffect(() => {
    if (contentHeight) {
      const calculatedScrollY = contentHeight - TABLE_SCROLL_CONFIG.offsetY;
      setTableScrollY(Math.max(calculatedScrollY, TABLE_SCROLL_CONFIG.minY));
    }
  }, [contentHeight]);

  return {
    tableScrollY,
    sortedInfo,
    filteredInfo,
    handleTableChange,
    getSortOrder,
    resetSortAndFilter,
    setTableScrollY,
  };
}
