/**
 * 任务相关常量配置
 */

/**
 * 任务状态选项
 */
export const TASK_STATUS_OPTIONS = [
  { label: '启用', value: 'enabled' },
  { label: '禁用', value: 'disabled' },
] as const;

/**
 * 星期选项
 */
export const WEEKDAY_OPTIONS = [
  { label: '周一', value: '1' },
  { label: '周二', value: '2' },
  { label: '周三', value: '3' },
  { label: '周四', value: '4' },
  { label: '周五', value: '5' },
  { label: '周六', value: '6' },
  { label: '周日', value: '7' },
] as const;

/**
 * 频率选项
 */
export const FREQUENCY_OPTIONS = [
  { label: '每天', value: 'daily' },
  { label: '每周', value: 'weekly' },
  { label: '每月', value: 'monthly' },
  { label: '自定义', value: 'custom' },
] as const;

/**
 * 数据库类型选项
 */
export const DB_TYPE_OPTIONS = [
  { label: 'MySQL', value: 'mysql' },
  { label: 'Oracle', value: 'oracle' },
] as const;

/**
 * 告警类型选项
 */
export const ALERT_TYPE_OPTIONS = [
  { label: '存在性检查', value: 'isExist' },
  { label: '相等性检查', value: 'isEqual' },
] as const;

/**
 * 告警级别选项
 */
export const ALERT_SEVERITY_OPTIONS = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '严重', value: 'critical' },
] as const;

/**
 * 发送类型选项
 */
export const SEND_TYPE_OPTIONS = [
  { label: 'Kafka', value: 'kafka' },
  { label: 'Prometheus', value: 'prometheus' },
] as const;

/**
 * Oracle连接方式选项
 */
export const ORACLE_CONNECT_METHOD_OPTIONS = [
  { label: 'SID', value: 'sid' },
  { label: 'Service', value: 'service' },
] as const;

/**
 * 频率单位选项
 */
export const FREQUENCY_UNIT_OPTIONS = [
  { label: '秒', value: '秒' },
  { label: '分', value: '分' },
  { label: '时', value: '时' },
  { label: '日', value: '日' },
] as const;

/**
 * 重试频率单位选项
 */
export const RETRY_FREQUENCY_UNIT_OPTIONS = [
  { label: '秒', value: '秒' },
  { label: '分钟', value: '分钟' },
  { label: '小时', value: '小时' },
] as const;

/**
 * 默认重试次数
 */
export const DEFAULT_RETRY_NUM = '3' as const;

/**
 * 默认频率配置
 */
export const DEFAULT_FREQUENCY = {
  value: 1,
  unit: '分',
} as const;

/**
 * 默认重试频率配置
 */
export const DEFAULT_RETRY_FREQUENCY = {
  value: 5,
  unit: '分钟',
} as const;
