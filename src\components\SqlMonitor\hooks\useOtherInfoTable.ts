/**
 * 其他信息表格管理Hook
 * 管理表格的排序、筛选、分页等状态
 */

import { useState, useCallback, useEffect } from 'react';
import type { TableProps } from 'antd';
import type { SorterResult, FilterValue, TablePaginationConfig } from 'antd/es/table/interface';
import type { OtherInfo } from '../types';
import { TABLE_SCROLL_CONFIG } from '../constants';

export interface UseOtherInfoTableOptions {
  /** 内容高度（用于计算表格滚动高度） */
  contentHeight?: number;
  /** 是否启用排序 */
  enableSort?: boolean;
  /** 是否启用筛选 */
  enableFilter?: boolean;
}

export interface UseOtherInfoTableReturn {
  /** 表格滚动高度 */
  tableScrollY: number;
  /** 排序状态 */
  sortedInfo: SorterResult<OtherInfo> | SorterResult<OtherInfo>[];
  /** 筛选状态 */
  filteredInfo: Record<string, FilterValue | null>;
  /** 表格变化处理函数 */
  handleTableChange: TableProps<OtherInfo>['onChange'];
  /** 获取排序信息的辅助函数 */
  getSortOrder: (field: string) => 'ascend' | 'descend' | null;
  /** 重置排序和筛选 */
  resetSortAndFilter: () => void;
  /** 设置表格滚动高度 */
  setTableScrollY: (height: number) => void;
}

/**
 * 其他信息表格管理Hook
 */
export function useOtherInfoTable(options: UseOtherInfoTableOptions = {}): UseOtherInfoTableReturn {
  const { contentHeight, enableSort = true, enableFilter = true } = options;

  // 表格滚动高度状态
  const [tableScrollY, setTableScrollY] = useState<number>(TABLE_SCROLL_CONFIG.defaultY);

  // 排序状态
  const [sortedInfo, setSortedInfo] = useState<SorterResult<OtherInfo> | SorterResult<OtherInfo>[]>({});

  // 筛选状态
  const [filteredInfo, setFilteredInfo] = useState<Record<string, FilterValue | null>>({});

  // 根据传入的contentHeight计算表格滚动高度
  useEffect(() => {
    if (contentHeight) {
      const calculatedScrollY = contentHeight - TABLE_SCROLL_CONFIG.offsetY;
      setTableScrollY(Math.max(calculatedScrollY, TABLE_SCROLL_CONFIG.minY));
    }
  }, [contentHeight]);

  // 获取排序信息的辅助函数
  const getSortOrder = useCallback(
    (field: string): 'ascend' | 'descend' | null => {
      if (Array.isArray(sortedInfo)) {
        const sorter = sortedInfo.find(s => s.field === field);
        return sorter?.order || null;
      }
      return sortedInfo.field === field ? sortedInfo.order || null : null;
    },
    [sortedInfo]
  );

  // 表格变化处理函数
  const handleTableChange: TableProps<OtherInfo>['onChange'] = useCallback(
    (pagination: TablePaginationConfig, filters, sorter) => {
      console.log('表格状态变化:', { pagination, filters, sorter });

      // 更新排序状态
      if (enableSort) {
        setSortedInfo(sorter as SorterResult<OtherInfo> | SorterResult<OtherInfo>[]);
      }

      // 更新筛选状态
      if (enableFilter) {
        setFilteredInfo(filters);
      }
    },
    [enableSort, enableFilter]
  );

  // 重置排序和筛选
  const resetSortAndFilter = useCallback(() => {
    setSortedInfo({});
    setFilteredInfo({});
  }, []);

  return {
    tableScrollY,
    sortedInfo,
    filteredInfo,
    handleTableChange,
    getSortOrder,
    resetSortAndFilter,
    setTableScrollY,
  };
}
