import React, { useState, useEffect } from 'react';
import { Button, Space, Drawer, App, Card, Typography, Tag, Tooltip, Flex, Divider, Input } from 'antd';
import { ClearOutlined, DeleteOutlined, CheckOutlined, SendOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { AlertSend, KafkaConfig, PrometheusConfig } from '../../types';
import { formStyles } from '../../styles';
import AlertSendTable from '../AlertSendTable';

interface NotificationConfigTabProps {
  alertSends: AlertSend[];
  onAlertSendsChange: (alertSends: AlertSend[]) => void;
}

/**
 * 告警发送配置标签页组件
 * 管理告警发送方式的配置
 */
const NotificationConfigTab: React.FC<NotificationConfigTabProps> = ({ alertSends, onAlertSendsChange }) => {
  const { message } = App.useApp();

  // 动态高度状态
  const [containerHeight, setContainerHeight] = useState(600);

  // 抽屉状态管理
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRows, setSelectedRows] = useState<AlertSend[]>(alertSends);
  const [selectedRowsFromChild, setSelectedRowsFromChild] = useState<AlertSend[]>([]);

  // 计算动态高度
  useEffect(() => {
    const calculateHeight = () => {
      // 100vh - (60+84+46+57) = 100vh - 247px
      const dynamicHeight = window.innerHeight - 247;
      setContainerHeight(Math.max(400, dynamicHeight)); // 最小高度400px
    };

    // 初次加载时计算
    calculateHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', calculateHeight);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, []);

  // 删除告警发送配置
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = selectedRows.filter((_, i) => i !== index);
    setSelectedRows(newAlertSends);
  };

  // 清空所有选择
  const handleClearAll = () => {
    setSelectedRows([]);
    message.success('已清空所有选择');
  };

  // 处理选择发送按钮点击
  const handleSelectAlertSend = () => {
    setDrawerVisible(true);
  };

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setDrawerVisible(false);
  };

  // 处理抽屉内的选择确认
  const handleDrawerSelectionConfirm = () => {
    console.log('确认选择时的selectedRows:', selectedRowsFromChild);

    setSelectedRows(selectedRowsFromChild);

    onAlertSendsChange(selectedRows);

    handleDrawerClose();
    message.success(`已选择 ${selectedRowsFromChild.length} 个告警发送配置`);
  };

  // 处理选择变化（从AlertSendTable传递过来）
  const handleSelectionChange = (selectedAlertSends: AlertSend[]) => {
    console.log('NotificationConfigTab 接收到选择变化:', selectedAlertSends);
    // 接收子组件传递的选择数据，保存为副本，
    // 当点击确认按钮时在更新当前页面数据
    // 因为当前页面数据会直接传递给子组件
    // 这样避免了无限循环渲染
    setSelectedRowsFromChild(selectedAlertSends);
  };

  // 复制到剪贴板的函数
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success(`${label}已复制到剪贴板`);
    } catch {
      message.error('复制失败');
    }
  };

  const { Text } = Typography;

  // 可复制字段组件 - 使用 Antd 组件
  const CopyableField: React.FC<{
    label: string;
    value: string;
    canCopy?: boolean;
    isPassword?: boolean;
  }> = ({ label, value, canCopy = true, isPassword = false }) => {
    const isTypeField = label === '类型';
    const isKafka = value.toLowerCase() === 'kafka';

    return (
      <div>
        <Text type="secondary" className="text-xs uppercase tracking-wider block mb-0.5">
          {label}
        </Text>
        {isTypeField ? (
          <Tag color={isKafka ? 'blue' : 'green'} className="m-0 uppercase font-semibold">
            {value}
          </Tag>
        ) : isPassword ? (
          <Tooltip title={`点击复制${label}`}>
            <Input.Password
              value={value || '未设置'}
              readOnly
              size="small"
              className="text-xs cursor-pointer hover:border-blue-300 hover:shadow-sm transition-all duration-200"
              style={{
                minHeight: '28px',
                fontSize: '12px',
              }}
              onClick={() => copyToClipboard(value, label)}
              visibilityToggle={{
                visible: undefined,
                onVisibleChange: () => {},
              }}
            />
          </Tooltip>
        ) : canCopy ? (
          <Tooltip title={`点击复制${label}`}>
            <Text
              copyable={{ text: value, tooltips: false }}
              code
              ellipsis={{ tooltip: value }}
              className="block w-full px-2 py-1 bg-white border border-gray-200 rounded text-xs cursor-pointer transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-sm pr-8"
              onClick={() => copyToClipboard(value, label)}
              style={{
                minHeight: '28px',
                lineHeight: '14px',
                display: 'flex',
                alignItems: 'center',
              }}
            >
              {value}
            </Text>
          </Tooltip>
        ) : (
          <Text
            ellipsis={{ tooltip: value }}
            className="block w-full px-2 py-1 bg-white border border-gray-200 rounded text-xs text-gray-600"
            style={{
              minHeight: '28px',
              lineHeight: '14px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {value}
          </Text>
        )}
      </div>
    );
  };

  const renderAlertSendItem = (alertSend: AlertSend, index: number) => {
    // 解析配置信息
    let parsedConfig: KafkaConfig | PrometheusConfig | null = null;
    try {
      parsedConfig = typeof alertSend.config === 'string' ? JSON.parse(alertSend.config) : alertSend.config;
    } catch (error) {
      console.error('解析配置信息失败:', error);
      parsedConfig = null;
    }

    return (
      <Card
        key={alertSend.id}
        hoverable
        className="relative transition-all duration-300 shadow-sm hover:shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 hover:from-blue-50 hover:to-indigo-50"
        styles={{
          body: {
            padding: '12px',
            background: 'transparent',
          },
        }}
      >
        {/* 操作按钮 - 右上角 */}
        <div className="absolute top-2 right-2 z-10">
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteAlertSend(index)}
            className="w-6 h-6 rounded-full flex items-center justify-center p-0 shadow-sm hover:shadow-md bg-white/80 hover:bg-red-50 border border-red-100 hover:border-red-200 transition-all duration-200"
            title="取消选择"
          ></Button>
        </div>

        {/* 名称和ID */}
        <div className="mb-2 pr-7">
          <div className="flex items-center justify-between">
            <Typography.Title level={5} className="m-0 text-gray-800 font-medium text-sm truncate">
              {alertSend.name}
            </Typography.Title>
            <div className="flex items-center gap-1 ml-2">
              <div className="w-1 h-1 rounded-full bg-green-400"></div>
              <Text type="secondary" className="text-xs text-gray-500">
                #{alertSend.id}
              </Text>
            </div>
          </div>
        </div>

        {/* 字段网格 - 根据类型动态调整 */}
        <div className={`grid gap-1.5 mb-2 ${alertSend.receive_type === 'kafka' ? 'grid-cols-2' : 'grid-cols-1'}`}>
          {/* 发送类型 - 不可复制 */}
          <div className="bg-white/50 rounded p-1.5 border border-gray-100">
            <CopyableField label="类型" value={alertSend.receive_type} canCopy={false} />
          </div>

          {/* Topic - 仅 Kafka 类型显示 */}
          {alertSend.receive_type === 'kafka' && parsedConfig && (
            <div className="bg-white/50 rounded p-1.5 border border-gray-100">
              <CopyableField label="Topic" value={(parsedConfig as KafkaConfig).topic || '未设置'} canCopy={true} />
            </div>
          )}
        </div>

        {/* 连接地址信息 - 单独一行，可复制 */}
        {parsedConfig && parsedConfig.address && (
          <div className="mb-1.5 bg-blue-50/50 rounded p-1.5 border border-blue-100">
            <CopyableField label="连接地址" value={parsedConfig.address} canCopy={true} />
          </div>
        )}

        {/* 认证信息 - 仅在有用户名时显示 */}
        {parsedConfig && parsedConfig.username && (
          <div className="grid grid-cols-2 gap-1.5 mb-1.5">
            <div className="bg-green-50/50 rounded p-1.5 border border-green-100">
              <CopyableField label="用户名" value={parsedConfig.username} canCopy={true} />
            </div>
            <div className="bg-orange-50/50 rounded p-1.5 border border-orange-100">
              <CopyableField label="密码" value={parsedConfig.password || ''} canCopy={true} isPassword={true} />
            </div>
          </div>
        )}

        {/* 时间信息 */}
        <Divider className="my-2 border-gray-200" />
        <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded p-1.5 border border-gray-100">
          <div className="flex justify-between text-xs">
            <div className="flex items-center gap-1">
              <div className="w-1 h-1 rounded-full bg-blue-400"></div>
              <Text className="text-xs font-mono text-gray-600">{alertSend.create_time}</Text>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-1 h-1 rounded-full bg-green-400"></div>
              <Text className="text-xs font-mono text-gray-600">{alertSend.update_time}</Text>
            </div>
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div className={`${formStyles.tabContent} relative flex flex-col border border-gray-200 rounded-lg bg-white`} style={{ height: `${containerHeight}px` }}>
      {/* 固定顶部 - 标题和操作按钮 */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-5 py-4 rounded-t-lg">
        <Flex justify="space-between" align="center">
          <Flex align="center" gap="middle">
            <Typography.Title level={4} className="m-0">
              告警发送配置
            </Typography.Title>
            <Text type="secondary">共 {selectedRows.length} 个配置</Text>
          </Flex>
          <Space>
            {selectedRows.length > 0 && (
              <Button danger size="small" type="text" icon={<ClearOutlined />} onClick={handleClearAll}>
                清空选择
              </Button>
            )}
            <Button size="small" type="text" icon={<SendOutlined />} onClick={handleSelectAlertSend}>
              选择发送
            </Button>
          </Space>
        </Flex>
      </div>

      {/* 可滚动的中间内容区域 */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden px-5 py-4 bg-gray-50" style={{ maxHeight: `calc(${containerHeight}px - 70px)` }}>
        {selectedRows.length === 0 ? (
          <div className="text-center py-20 px-8 text-gray-400 bg-gradient-to-br from-gray-50 to-slate-50 rounded-xl border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors duration-300">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
              <SendOutlined className="text-2xl text-gray-400" />
            </div>
            <div className="text-lg mb-2 font-medium text-gray-600">暂无告警发送配置</div>
            <div className="text-sm text-gray-500">请点击"选择发送"添加配置</div>
          </div>
        ) : (
          <div
            className="gap-3 pb-3"
            style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
              maxWidth: '100%',
            }}
          >
            {selectedRows.map((alertSend, index) => renderAlertSendItem(alertSend, index))}
          </div>
        )}
      </div>

      {/* 选择发送抽屉 */}
      <Drawer
        title={
          <div className="flex justify-between items-center w-full">
            <span>选择告警发送配置</span>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleDrawerSelectionConfirm}
              // disabled={selectedRows.length === 0}
            >
              确认选择 {selectedRowsFromChild.length > 0 && `(${selectedRowsFromChild.length})`}
            </Button>
          </div>
        }
        width="80%"
        open={drawerVisible}
        onClose={handleDrawerClose}
        maskClosable={false}
      >
        <AlertSendTable contentHeight={800} isSelectionMode={true} selectedRows={selectedRows} onSelectionConfirm={handleSelectionChange} />
      </Drawer>
    </div>
  );
};

export default NotificationConfigTab;
