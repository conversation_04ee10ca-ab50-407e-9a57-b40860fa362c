import { useState, useEffect, useCallback, useRef } from 'react';
import { useDebouncedCallback } from '../../hooks/useDebounce';

/**
 * 内容区域高度管理 Hook
 */
export const useContentHeight = () => {
  const [contentHeight, setContentHeight] = useState<number>(0);
  const contentRef = useRef<HTMLDivElement>(null);

  // 创建高度更新函数
  const updateContentHeight = useCallback(() => {
    if (contentRef.current) {
      const height = contentRef.current.offsetHeight;
      setContentHeight(height);
    }
  }, []);

  // 创建防抖版本的高度更新函数
  const debouncedUpdateContentHeight = useDebouncedCallback(updateContentHeight, 150);

  // 监听Content高度变化并更新状态
  useEffect(() => {
    // 初始更新（不需要防抖）
    updateContentHeight();

    // 监听窗口大小变化（使用防抖）
    const handleResize = () => {
      debouncedUpdateContentHeight();
    };

    window.addEventListener('resize', handleResize);

    // 使用ResizeObserver监听Content元素大小变化（使用防抖）
    let resizeObserver: ResizeObserver | null = null;
    if (contentRef.current && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(() => {
        debouncedUpdateContentHeight();
      });
      resizeObserver.observe(contentRef.current);
    }

    return () => {
      window.removeEventListener('resize', handleResize);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [updateContentHeight, debouncedUpdateContentHeight]);

  return {
    contentHeight,
    contentRef,
  };
};
