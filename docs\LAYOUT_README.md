# 现代化可收缩侧边栏布局

这是一个基于 React + Ant Design 构建的现代化管理系统布局，具有可收缩的侧边栏和完整的导航功能。

## 功能特性

### 🎨 现代化设计

- 简洁优雅的界面设计
- 响应式布局，支持移动端
- 流畅的动画过渡效果
- 统一的设计语言

### 📱 可收缩侧边栏

- 点击按钮可收缩/展开侧边栏
- 收缩时显示图标，展开时显示完整菜单
- 平滑的动画过渡
- 自动适应内容宽度

### 🧭 智能导航

- 自动高亮当前页面菜单项
- 支持多级菜单结构
- 路由自动同步
- 面包屑导航（可扩展）

### 👤 用户信息区域

- 用户头像和姓名显示
- 通知消息提醒（带红点提示）
- 用户下拉菜单（个人资料、设置、退出）
- 在线状态指示

### 📊 页面示例

- **仪表板页面**: 数据统计、图表展示、最近活动
- **用户管理页面**: 用户列表、增删改查、角色管理
- **任务管理页面**: 任务列表、状态管理
- **团队协作页面**: 团队成员、项目进度、活动时间线

## 技术栈

- **React 18**: 现代化的前端框架
- **TypeScript**: 类型安全的开发体验
- **Ant Design 5**: 企业级 UI 组件库
- **React Router Dom**: 客户端路由管理
- **Vite**: 快速的构建工具

## 项目结构

```
src/
├── layouts/
│   ├── DefaultLayout.tsx          # 主布局组件
│   └── DefaultLayout.module.css   # 布局样式文件
├── pages/
│   ├── DashboardPage.tsx          # 仪表板页面
│   ├── UserManagementPage.tsx     # 用户管理页面
│   └── TeamCollaborationPage.tsx  # 团队协作页面
├── components/                    # 可复用组件
├── router.tsx                     # 路由配置
└── App.tsx                       # 应用入口
```

## 核心组件说明

### DefaultLayout 组件

主布局组件，包含以下功能：

- 侧边栏收缩/展开控制
- 菜单导航和路由跳转
- 用户信息展示
- 通知消息管理

### 关键特性

1. **状态管理**: 使用 React Hooks 管理收缩状态
2. **路由集成**: 与 React Router 深度集成
3. **主题支持**: 使用 Ant Design 主题系统
4. **响应式**: 支持不同屏幕尺寸

## 使用方法

### 1. 启动项目

```bash
pnpm install
pnpm dev
```

### 2. 访问页面

- 仪表板: http://localhost:5174/
- 用户管理: http://localhost:5174/users
- 任务管理: http://localhost:5174/tasks
- 团队协作: http://localhost:5174/teams

### 3. 自定义菜单

在 `DefaultLayout.tsx` 中修改 `menuItems` 数组：

```typescript
const menuItems: MenuProps['items'] = [
  {
    key: 'dashboard',
    icon: <DashboardOutlined />,
    label: '仪表板',
  },
  // 添加更多菜单项...
];
```

### 4. 添加新页面

1. 在 `src/pages/` 目录下创建新的页面组件
2. 在 `router.tsx` 中添加路由配置
3. 在布局组件中添加对应的菜单项

## 自定义配置

### 修改主题色彩

在 Ant Design 配置中修改主题：

```typescript
import { ConfigProvider } from 'antd';

<ConfigProvider
  theme={{
    token: {
      colorPrimary: '#1890ff', // 主色调
      borderRadius: 6,         // 圆角大小
    },
  }}
>
  <App />
</ConfigProvider>
```

### 调整布局尺寸

在 `DefaultLayout.tsx` 中修改相关样式：

```typescript
<Sider
  width={240}  // 侧边栏宽度
  // 其他配置...
/>
```

## 响应式支持

布局自动适配不同屏幕尺寸：

- **桌面端**: 完整显示所有功能
- **平板端**: 自动调整间距和布局
- **移动端**: 侧边栏变为抽屉模式

## 扩展建议

1. **添加面包屑导航**: 在顶部显示当前页面路径
2. **主题切换**: 支持明暗主题切换
3. **多语言支持**: 集成 i18n 国际化
4. **权限控制**: 根据用户角色显示不同菜单
5. **个性化设置**: 允许用户自定义布局偏好

## 性能优化

- 使用 React.memo 优化组件渲染
- 路由懒加载减少初始包大小
- 图标按需加载
- CSS 模块化避免样式冲突

这个布局为现代化管理系统提供了坚实的基础，可以根据具体需求进行进一步的定制和扩展。
