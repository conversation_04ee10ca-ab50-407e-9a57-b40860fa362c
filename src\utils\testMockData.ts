import { mockDbConnectionData, mockAlertSendData, mockOtherInfoData } from '../services/taskService';

/**
 * 测试生成的模拟数据
 */
export function testMockData() {
  console.log('=== 测试模拟数据生成 ===');

  // 测试数据库连接数据
  console.log('\n📊 数据库连接数据统计:');
  console.log(`总数量: ${mockDbConnectionData.length}`);
  console.log(`MySQL数量: ${mockDbConnectionData.filter(db => db.db_type === 'mysql').length}`);
  console.log(`Oracle数量: ${mockDbConnectionData.filter(db => db.db_type === 'oracle').length}`);
  console.log('前3条数据示例:');
  mockDbConnectionData.slice(0, 3).forEach((db, index) => {
    console.log(`  ${index + 1}. ${db.name} (${db.db_type}) - ${db.host}:${db.port}`);
  });

  // 测试告警发送数据
  console.log('\n📨 告警发送数据统计:');
  console.log(`总数量: ${mockAlertSendData.length}`);
  console.log(`Kafka数量: ${mockAlertSendData.filter(alert => alert.type === 'kafka').length}`);
  console.log(`Prometheus数量: ${mockAlertSendData.filter(alert => alert.type === 'prometheus').length}`);
  console.log('前3条数据示例:');
  mockAlertSendData.slice(0, 3).forEach((alert, index) => {
    const address = alert.type === 'kafka' ? alert.kafka_receiver.address : alert.prometheus_receiver.address;
    console.log(`  ${index + 1}. ${alert.name} (${alert.type}) - ${address}`);
  });

  // 测试其他信息数据
  console.log('\n🏢 其他信息数据统计:');
  console.log(`总数量: ${mockOtherInfoData.length}`);
  const businessSystems = [...new Set(mockOtherInfoData.map(info => info.business))];
  console.log(`业务系统类型数量: ${businessSystems.length}`);
  console.log('业务系统类型:', businessSystems.slice(0, 5).join(', '));
  console.log('前3条数据示例:');
  mockOtherInfoData.slice(0, 3).forEach((info, index) => {
    console.log(`  ${index + 1}. ${info.name} - ${info.business} (${info.hostname})`);
  });

  console.log('\n✅ 模拟数据生成测试完成!');
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined') {
  testMockData();
}
