import React, { useState, useEffect } from 'react';
import { Button, Space, Drawer, App, Card, Typography, Tag, Tooltip, Flex, Divider } from 'antd';
import { ClearOutlined, DeleteOutlined, CheckOutlined, SendOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';

// 导入重构后的模块
import type { AlertSend, KafkaConfig, PrometheusConfig } from '../../types';
import { formStyles } from '../../styles';
import AlertSendTable from '../AlertSendTable';

interface NotificationConfigTabProps {
  alertSends: AlertSend[];
  onAlertSendsChange: (alertSends: AlertSend[]) => void;
}

/**
 * 告警发送配置标签页组件
 * 管理告警发送方式的配置
 */
const NotificationConfigTab: React.FC<NotificationConfigTabProps> = ({ alertSends, onAlertSendsChange }) => {
  const { message } = App.useApp();

  // 动态高度状态
  const [containerHeight, setContainerHeight] = useState(600);

  // 抽屉状态管理
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRows, setSelectedRows] = useState<AlertSend[]>(alertSends);
  const [selectedRowsFromChild, setSelectedRowsFromChild] = useState<AlertSend[]>([]);

  // 计算动态高度
  useEffect(() => {
    const calculateHeight = () => {
      // 100vh - (60+84+46+57) = 100vh - 247px
      const dynamicHeight = window.innerHeight - 247;
      setContainerHeight(Math.max(400, dynamicHeight)); // 最小高度400px
    };

    // 初次加载时计算
    calculateHeight();

    // 监听窗口大小变化
    window.addEventListener('resize', calculateHeight);

    // 清理事件监听器
    return () => {
      window.removeEventListener('resize', calculateHeight);
    };
  }, []);

  // 删除告警发送配置
  const handleDeleteAlertSend = (index: number) => {
    const newAlertSends = selectedRows.filter((_, i) => i !== index);
    setSelectedRows(newAlertSends);
  };

  // 清空所有选择
  const handleClearAll = () => {
    setSelectedRows([]);
    message.success('已清空所有选择');
  };

  // 处理选择发送按钮点击
  const handleSelectAlertSend = () => {
    setDrawerVisible(true);
  };

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setDrawerVisible(false);
  };

  // 处理抽屉内的选择确认
  const handleDrawerSelectionConfirm = () => {
    console.log('确认选择时的selectedRows:', selectedRowsFromChild);

    setSelectedRows(selectedRowsFromChild);

    onAlertSendsChange(selectedRows);

    handleDrawerClose();
    message.success(`已选择 ${selectedRowsFromChild.length} 个告警发送配置`);
  };

  // 处理选择变化（从AlertSendTable传递过来）
  const handleSelectionChange = (selectedAlertSends: AlertSend[]) => {
    console.log('NotificationConfigTab 接收到选择变化:', selectedAlertSends);
    // 接收子组件传递的选择数据，保存为副本，
    // 当点击确认按钮时在更新当前页面数据
    // 因为当前页面数据会直接传递给子组件
    // 这样避免了无限循环渲染
    setSelectedRowsFromChild(selectedAlertSends);
  };

  // 复制到剪贴板的函数
  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      message.success(`${label}已复制到剪贴板`);
    } catch {
      message.error('复制失败');
    }
  };

  const { Text } = Typography;

  // 可复制字段组件 - 使用 Antd 组件
  const CopyableField: React.FC<{
    label: string;
    value: string;
    canCopy?: boolean;
    isPassword?: boolean;
  }> = ({ label, value, canCopy = true, isPassword = false }) => {
    const [showPassword, setShowPassword] = useState(false);
    const isTypeField = label === '类型';
    const isKafka = value.toLowerCase() === 'kafka';

    // 文本截断函数
    const truncateText = (text: string, maxLength: number = 10) => {
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    };

    // 获取显示的值
    const getDisplayValue = () => {
      if (isPassword) {
        if (showPassword) {
          return value;
        } else {
          return value ? '••••••••' : '未设置';
        }
      }
      return truncateText(value);
    };

    const displayValue = getDisplayValue();

    return (
      <div>
        <Text type="secondary" className="text-xs uppercase tracking-wider block mb-1">
          {label}
        </Text>
        {isTypeField ? (
          <Tag color={isKafka ? 'blue' : 'green'} className="m-0 uppercase font-semibold">
            {value}
          </Tag>
        ) : canCopy ? (
          <Tooltip title={value.length > 30 ? value : `点击复制${label}`}>
            <div className="relative group w-full">
              <Text
                copyable={{ text: value, tooltips: false }}
                code
                className={`block w-full px-3 py-2 bg-white border border-gray-200 rounded-md text-xs cursor-pointer transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-sm group-hover:shadow-sm overflow-hidden ${isPassword ? 'pr-14' : 'pr-10'}`}
                onClick={() => copyToClipboard(value, label)}
                style={{
                  whiteSpace: 'nowrap',
                  textOverflow: 'ellipsis',
                  minHeight: '32px',
                  lineHeight: '16px',
                  display: 'flex',
                  alignItems: 'center',
                }}
              >
                {displayValue}
              </Text>
              {isPassword && (
                <Button
                  type="text"
                  size="small"
                  icon={showPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  onClick={e => {
                    e.stopPropagation();
                    setShowPassword(!showPassword);
                  }}
                  className="absolute right-8 top-1/2 transform -translate-y-1/2 w-5 h-5 flex items-center justify-center p-0 text-gray-400 hover:text-blue-500 transition-colors duration-200"
                  style={{ zIndex: 10 }}
                />
              )}
            </div>
          </Tooltip>
        ) : (
          <Text
            className="block w-full px-3 py-2 bg-white border border-gray-200 rounded-md text-xs text-gray-600 overflow-hidden"
            style={{
              whiteSpace: 'nowrap',
              textOverflow: 'ellipsis',
              minHeight: '32px',
              lineHeight: '16px',
              display: 'flex',
              alignItems: 'center',
            }}
          >
            {displayValue}
          </Text>
        )}
      </div>
    );
  };

  const renderAlertSendItem = (alertSend: AlertSend, index: number) => {
    // 解析配置信息
    let parsedConfig: KafkaConfig | PrometheusConfig | null = null;
    try {
      parsedConfig = typeof alertSend.config === 'string' ? JSON.parse(alertSend.config) : alertSend.config;
    } catch (error) {
      console.error('解析配置信息失败:', error);
      parsedConfig = null;
    }

    return (
      <Card
        key={alertSend.id}
        hoverable
        className="relative transition-all duration-300 shadow-sm hover:shadow-lg border-0 bg-gradient-to-br from-white to-gray-50 hover:from-blue-50 hover:to-indigo-50"
        styles={{
          body: {
            padding: '24px',
            background: 'transparent',
          },
        }}
      >
        {/* 操作按钮 - 右上角 */}
        <div className="absolute top-4 right-4 z-10">
          <Button
            type="text"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteAlertSend(index)}
            className="w-7 h-7 rounded-full flex items-center justify-center p-0 shadow-sm hover:shadow-md bg-white/80 hover:bg-red-50 border border-red-100 hover:border-red-200 transition-all duration-200"
            title="取消选择"
          ></Button>
        </div>

        {/* 名称和ID */}
        <div className="mb-6 pr-12">
          <div className="flex items-center gap-2 mb-2">
            <Typography.Title level={4} className="m-0 text-gray-800 font-semibold">
              {alertSend.name}
            </Typography.Title>
            <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
          </div>
          <Text type="secondary" className="text-xs bg-gray-100 px-2 py-1 rounded-full">
            ID: {alertSend.id}
          </Text>
        </div>

        {/* 字段网格 - 根据类型动态调整 */}
        <div className={`grid gap-4 mb-5 ${alertSend.receive_type === 'kafka' ? 'grid-cols-2' : 'grid-cols-1'}`}>
          {/* 发送类型 - 不可复制 */}
          <div className="bg-white/50 rounded-lg p-3 border border-gray-100">
            <CopyableField label="类型" value={alertSend.receive_type} canCopy={false} />
          </div>

          {/* Topic - 仅 Kafka 类型显示 */}
          {alertSend.receive_type === 'kafka' && parsedConfig && (
            <div className="bg-white/50 rounded-lg p-3 border border-gray-100">
              <CopyableField label="Topic" value={(parsedConfig as KafkaConfig).topic || '未设置'} canCopy={true} />
            </div>
          )}
        </div>

        {/* 连接地址信息 - 单独一行，可复制 */}
        {parsedConfig && parsedConfig.address && (
          <div className="mb-4 bg-blue-50/50 rounded-lg p-3 border border-blue-100">
            <CopyableField label="连接地址" value={parsedConfig.address} canCopy={true} />
          </div>
        )}

        {/* 认证信息 - 仅在有用户名时显示 */}
        {parsedConfig && parsedConfig.username && (
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="bg-green-50/50 rounded-lg p-3 border border-green-100">
              <CopyableField label="用户名" value={parsedConfig.username} canCopy={true} />
            </div>
            <div className="bg-orange-50/50 rounded-lg p-3 border border-orange-100">
              <CopyableField label="密码" value={parsedConfig.password || ''} canCopy={true} isPassword={true} />
            </div>
          </div>
        )}

        {/* 时间信息 */}
        <Divider className="my-5 border-gray-200" />
        <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg p-4 border border-gray-100">
          <Flex justify="space-between" gap="middle">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-blue-400"></div>
              <div>
                <Text type="secondary" className="text-xs uppercase tracking-wider block mb-1 font-medium">
                  创建时间
                </Text>
                <Text className="text-xs font-mono text-gray-700">{alertSend.create_time}</Text>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 rounded-full bg-green-400"></div>
              <div>
                <Text type="secondary" className="text-xs uppercase tracking-wider block mb-1 font-medium">
                  更新时间
                </Text>
                <Text className="text-xs font-mono text-gray-700">{alertSend.update_time}</Text>
              </div>
            </div>
          </Flex>
        </div>
      </Card>
    );
  };

  return (
    <div className={`${formStyles.tabContent} relative flex flex-col border border-gray-200 rounded-lg bg-white`} style={{ height: `${containerHeight}px` }}>
      {/* 固定顶部 - 标题和操作按钮 */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-5 py-4 rounded-t-lg">
        <Flex justify="space-between" align="center">
          <Flex align="center" gap="middle">
            <Typography.Title level={4} className="m-0">
              告警发送配置
            </Typography.Title>
            <Text type="secondary">共 {selectedRows.length} 个配置</Text>
          </Flex>
          <Space>
            {selectedRows.length > 0 && (
              <Button danger size="small" type="text" icon={<ClearOutlined />} onClick={handleClearAll}>
                清空选择
              </Button>
            )}
            <Button size="small" type="text" icon={<SendOutlined />} onClick={handleSelectAlertSend}>
              选择发送
            </Button>
          </Space>
        </Flex>
      </div>

      {/* 可滚动的中间内容区域 */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden px-5 py-4 bg-gray-50" style={{ maxHeight: `calc(${containerHeight}px - 70px)` }}>
        {selectedRows.length === 0 ? (
          <div className="text-center py-20 px-8 text-gray-400 bg-gradient-to-br from-gray-50 to-slate-50 rounded-xl border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors duration-300">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-200 rounded-full flex items-center justify-center">
              <SendOutlined className="text-2xl text-gray-400" />
            </div>
            <div className="text-lg mb-2 font-medium text-gray-600">暂无告警发送配置</div>
            <div className="text-sm text-gray-500">请点击"选择发送"添加配置</div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 pb-4">{selectedRows.map((alertSend, index) => renderAlertSendItem(alertSend, index))}</div>
        )}
      </div>

      {/* 选择发送抽屉 */}
      <Drawer
        title={
          <div className="flex justify-between items-center w-full">
            <span>选择告警发送配置</span>
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleDrawerSelectionConfirm}
              // disabled={selectedRows.length === 0}
            >
              确认选择 {selectedRowsFromChild.length > 0 && `(${selectedRowsFromChild.length})`}
            </Button>
          </div>
        }
        width="80%"
        open={drawerVisible}
        onClose={handleDrawerClose}
        maskClosable={false}
      >
        <AlertSendTable contentHeight={800} isSelectionMode={true} selectedRows={selectedRows} onSelectionConfirm={handleSelectionChange} />
      </Drawer>
    </div>
  );
};

export default NotificationConfigTab;
