import { createBrowserRouter } from 'react-router-dom';
import { DefaultLayout } from './layouts/DefaultLayout';
import DashboardPage from './pages/DashboardPage';
import UserManagementPage from './pages/UserManagementPage';
import TeamCollaborationPage from './pages/TeamCollaborationPage';
import TaskTablePage from './pages/TaskTablePage';
import AntdTable from './components/AntdTable';
import AlertTablePage from './pages/AlertTablePage';
import DBConnectionTablePage from './pages/DBConnectionTablePage';
import AlertSendTablePage from './pages/AlertSendTablePage';
import OtherInfoTablePage from './pages/OtherInfoTablePage';

export const router = createBrowserRouter([
  {
    path: '/',
    Component: DefaultLayout,
    children: [
      {
        index: true,
        Component: () => <DashboardPage />,
      },
      {
        path: 'dashboard',
        Component: () => <DashboardPage />,
        handle: { title: '仪表板' },
      },
      {
        path: 'users',
        Component: () => <UserManagementPage />,
        handle: { title: '用户管理' },
      },
      {
        path: 'tasks',
        Component: () => <TaskTablePage />,
        handle: { title: '任务管理' },
      },
      {
        path: 'alerts',
        Component: () => <AlertTablePage />,
        handle: { title: '告警管理' },
      },
      {
        path: 'db-connections',
        Component: () => <DBConnectionTablePage />,
        handle: { title: '数据库连接' },
      },
      {
        path: 'alert-sends',
        Component: () => <AlertSendTablePage />,
        handle: { title: '告警发送' },
      },
      {
        path: 'other-infos',
        Component: () => <OtherInfoTablePage />,
        handle: { title: '其他信息' },
      },
      {
        path: 'teams',
        Component: () => <TeamCollaborationPage />,
        handle: { title: '团队协作' },
      },
    ],
  },
  {
    path: '*',
    Component: () => <div>404 Not Found</div>,
  },
  {
    path: '/antd/table',
    Component: () => <AntdTable />,
  },
]);
