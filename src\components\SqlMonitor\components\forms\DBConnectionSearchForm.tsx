import React from 'react';
import { Form, Input, Select, Button, Row, Col } from 'antd';
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';

import type { DBConnectionSearchParams } from '../../types';
import { tableStyles } from '../../styles';

const { Option } = Select;

interface DBConnectionQuickSearchFormProps {
  form: FormInstance;
  onSubmit: (values: DBConnectionSearchParams) => void;
  onReset: () => void;
}

/**
 * 数据库连接快速搜索表单组件
 */
export const DBConnectionQuickSearchForm: React.FC<DBConnectionQuickSearchFormProps> = ({ form, onSubmit, onReset }) => {
  return (
    <div className={tableStyles.searchSection}>
      <Form form={form} onFinish={onSubmit} className="w-full">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="name" className="mb-0">
                <Input placeholder="请输入连接名称" prefix={<SearchOutlined className="text-gray-400" />} allowClear className="rounded-md" autoComplete="off" autoCorrect="off" autoCapitalize="off" spellCheck={false} />
              </Form.Item>
            </div>
          </Col>

          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="host" className="mb-0">
                <Input placeholder="请输入主机地址" allowClear className="rounded-md" autoComplete="off" autoCorrect="off" autoCapitalize="off" spellCheck={false} />
              </Form.Item>
            </div>
          </Col>

          <Col xs={24} sm={8} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <Form.Item name="db_type" className="mb-0">
                <Select placeholder="请选择数据库类型" allowClear className="w-full rounded-md">
                  <Option value="mysql">MySQL</Option>
                  <Option value="oracle">Oracle</Option>
                </Select>
              </Form.Item>
            </div>
          </Col>

          <Col xs={24} sm={24} md={6}>
            <div className="flex flex-col gap-1 h-full justify-center">
              <div className="flex gap-2 items-center h-full">
                <Button type="primary" htmlType="submit" icon={<SearchOutlined />} className={tableStyles.buttonPrimary}>
                  搜索
                </Button>
                <Button onClick={onReset} className="rounded-md flex-1" icon={<ReloadOutlined />}>
                  重置
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  );
};
