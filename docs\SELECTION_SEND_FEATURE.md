# 选择发送按钮功能实现

## 功能概述

实现了选择发送按钮功能，用户点击后可以打开一个抽屉，显示AlertSendTable表格内容，支持选择模式，用户可以选择多个告警发送配置并确认选择。

## 实现的功能

### 1. 选择发送按钮
- 在NotificationConfigTab组件中添加了"选择发送"按钮
- 点击按钮后打开抽屉，显示AlertSendTable表格

### 2. 抽屉显示
- 抽屉宽度为80%，标题为"选择告警发送配置"
- 抽屉内嵌入AlertSendTable组件，支持选择模式

### 3. 选择模式支持
- AlertSendTable组件新增`isSelectionMode`参数
- 选择模式下隐藏"新增告警发送"按钮
- 选择模式下隐藏"批量删除"按钮
- 在"取消全选"按钮旁边添加"确认选择"按钮

### 4. 初始选择状态
- 支持传入`selectedKeys`参数，预选择当前已选择的告警发送配置
- useSelection hook新增`initialSelectedKeys`参数支持

### 5. 选择确认功能
- 点击"确认选择"按钮时，回调父组件方法返回选择的数据
- 选择完成后关闭抽屉并显示成功消息

## 修改的文件

### 1. NotificationConfigTab.tsx
- 添加抽屉状态管理
- 实现选择发送按钮点击处理
- 添加选择确认回调处理
- 修复config字段的类型问题

### 2. AlertSendTable.tsx
- 新增选择模式相关参数
- 添加选择确认处理函数
- 传递选择模式参数给ActionButtons组件

### 3. AlertSendActionButtons.tsx
- 新增选择模式支持
- 选择模式下显示不同的按钮布局
- 添加"确认选择"按钮

### 4. useSelection.ts
- 新增`initialSelectedKeys`参数支持
- 添加初始选择状态处理逻辑

### 5. 类型定义修改
- 修复AlertSend接口中config字段的类型定义
- 从string类型改为KafkaConfig | PrometheusConfig联合类型

## 使用方式

1. 在任务管理页面中，点击"新增任务"或"编辑任务"
2. 切换到"告警发送"标签页
3. 点击"选择发送"按钮
4. 在打开的抽屉中选择需要的告警发送配置
5. 点击"确认选择"按钮完成选择
6. 选择的配置会显示在告警发送配置列表中

## 技术特点

- 支持跨页面选择
- 保持已选择状态的同步
- 类型安全的实现
- 良好的用户体验
- 可复用的选择模式设计

## 测试建议

1. 测试选择发送按钮的点击功能
2. 测试抽屉的打开和关闭
3. 测试选择模式下的按钮显示
4. 测试初始选择状态的正确性
5. 测试选择确认功能
6. 测试选择完成后的数据回调
