import { httpClient } from '@/services/http/client';
/**
 * 任务管理服务
 * 提供所有任务相关的API调用
 */

import type { TaskBasic, TaskBasicFormData, TaskBasicFormDataAdd, TaskBasicFormDataUpdateOrDelete, TaskBasicGroupFormData, TaskBasicSearchParams, TaskGroupSearchParams, TaskAlert, DBConnection, AlertSend, OtherInfo, TaskAlertSearchParams, ApiResponse, DBConnectionSearchParams, AlertSendSearchParams } from '../types';

import { mockDbConnectionData, mockOtherInfoData } from './mockData';

// 导入频率转换工具
import { parseFrequencyFromString } from '../../../utils/frequencyConverter';
import type { KafkaReceiver, PrometheusReceiver } from '@/types/task';

/**
 * 任务管理API服务类
 */
export class TaskService {
  /**
   * 获取任务列表
   * @param params 搜索参数
   * @returns 任务列表和总数
   */
  static async getTasks(params: TaskBasicSearchParams): Promise<ApiResponse<TaskBasic>> {
    console.log('获取任务列表参数:', params);

    // 模拟API调用延迟

    try {
      const response = await httpClient.post<ApiResponse<TaskBasicFormData>>('/api/v1/task/exec/select', params);

      console.log('获取任务列表响应:', response);

      if (!response || !response.data || response.data == null) {
        console.warn('获取任务数据失败：返回数据为空');
        return {
          data: [],
          total: 0,
          success: true,
        };
      }

      // 将 TaskBasicFormData 转换为 TaskBasic
      const tableData: TaskBasic[] = response.data.map((item: TaskBasicFormData) => {
        // 解析执行频率字符串为对象格式
        const parsedFrequency = parseFrequencyFromString(item.frequency, false);
        const parsedRetryFrequency = parseFrequencyFromString(item.retry_frequency, true);

        return {
          id: item.id,
          name: item.name,
          group_id: item.group_id,
          group_name: item.group_name,
          status: item.status as 'enabled' | 'disabled',
          start_time: item.start_time,
          end_time: item.end_time,
          weekday: item.weekday ? item.weekday.split(',') : [],
          frequency: parsedFrequency || { value: 0, unit: '分' }, // 使用默认值
          retry_num: item.retry_num,
          retry_frequency: parsedRetryFrequency || { value: 0, unit: '分钟' }, // 使用默认值
          alert_task_id: item.alert_task_id ? item.alert_task_id.split(',') : [],
          alert_send_id: item.alert_send_id ? item.alert_send_id.split(',') : [],
          db_connection_id: item.db_connection_id,
          other_info_id: item.other_info_id,
          create_time: item.create_time,
          update_time: item.update_time,
        };
      });

      return {
        data: tableData,
        total: response.total,
        success: true,
      };
    } catch (error) {
      console.error('获取任务列表失败:', error);
      throw error;
    }
  }

  /**
   * 获取任务分组列表
   * @param params 搜索参数
   * @returns 任务分组列表和总数
   */
  static async getTaskGroups(params: TaskGroupSearchParams): Promise<ApiResponse<TaskBasicGroupFormData>> {
    console.log('获取任务分组参数:', params);

    try {
      const response = await httpClient.post<ApiResponse<TaskBasicGroupFormData>>('/api/v1/task/group/select', params);

      console.log('获取任务分组响应:', response);

      return {
        data: response.data,
        total: response.total,
        success: true,
      };
    } catch (error) {
      console.error('获取任务分组失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有任务分组
   * @returns 所有任务分组列表
   */
  static async getTaskGroupAll(): Promise<ApiResponse<TaskBasicGroupFormData>> {
    try {
      const response = await httpClient.post<ApiResponse<TaskBasicGroupFormData>>('/api/v1/task/group/select/all', {});

      console.log('获取所有任务分组响应:', response);

      return {
        data: response.data,
        total: response.total,
        success: true,
      };
    } catch (error) {
      console.error('获取所有任务分组失败:', error);
      throw error;
    }
  }

  /**
   * 删除单个任务
   * @param id 任务ID
   * @returns 删除结果
   */
  static async deleteTask(id: number): Promise<ApiResponse<TaskBasicFormDataUpdateOrDelete>> {
    console.log('删除任务:', id);

    try {
      // 请求后端参数包装成map
      const params = {
        ids: [id],
      };

      const response = await httpClient.post<ApiResponse<TaskBasicFormDataUpdateOrDelete>>('/api/v1/task/exec/delete/id', params);

      console.log('删除任务响应:', response);
      return response;
    } catch (error) {
      console.error('删除任务失败:', error);
      throw error;
    }
  }

  /**
   * 批量删除任务
   * @param ids 任务ID数组
   * @returns 删除结果
   */
  static async batchDeleteTasks(ids: number[]): Promise<ApiResponse<TaskBasicFormDataUpdateOrDelete>> {
    console.log('批量删除任务:', ids);

    // 模拟API调用延迟

    try {
      // 请求后端参数包装成map
      const params = {
        ids: ids,
      };

      const response = await httpClient.post<ApiResponse<TaskBasicFormDataUpdateOrDelete>>('/api/v1/task/exec/delete/id', params);

      console.log('批量删除任务响应:', response);
      return response;
    } catch (error) {
      console.error('批量删除任务失败:', error);
      throw error;
    }
  }

  /**
   * 添加任务
   * @param taskData 任务数据
   * @returns 新创建的任务信息
   */
  static async addTask(taskData: TaskBasicFormDataAdd): Promise<TaskBasicFormDataAdd> {
    console.log('添加任务:', taskData);

    try {
      const response = await httpClient.post<TaskBasicFormDataAdd>('/api/v1/task/exec', taskData);

      console.log('添加任务响应:', response);
      return response;
    } catch (error) {
      console.error('添加任务失败:', error);
      throw error;
    }
  }

  /**
   * 更新任务
   * @param id 任务ID
   * @param taskData 任务数据
   * @returns 更新后的任务信息
   */
  static async updateTask(id: number, taskData: TaskBasicFormDataUpdateOrDelete): Promise<TaskBasicFormDataUpdateOrDelete> {
    console.log('更新任务:', id, taskData);

    try {
      const response = await httpClient.post<TaskBasicFormDataUpdateOrDelete>('/api/v1/task/exec/update', taskData);

      console.log('更新任务响应:', response);
      return response;
    } catch (error) {
      console.error('更新任务失败:', error);
      throw error;
    }
  }

  /**
   * 保存复合表单数据
   * @param data 表单数据
   * @returns 保存结果
   */
  static async saveComplexForm(data: TaskBasicFormDataAdd): Promise<TaskBasicFormDataAdd> {
    console.log('保存复合表单数据:', data);

    try {
      const response = await httpClient.post<TaskBasicFormDataAdd>('/api/v1/task/exec', data);

      console.log('保存复合表单响应:', response);
      return response;
    } catch (error) {
      console.error('保存复合表单失败:', error);
      throw error;
    }
  }

  /**
   * 更新复合表单数据
   * @param id 任务ID
   * @param data 表单数据
   * @returns 更新结果
   */
  static async updateComplexForm(id: number, data: TaskBasicFormDataUpdateOrDelete): Promise<TaskBasicFormDataUpdateOrDelete> {
    console.log('更新复合表单数据:', id, data);

    try {
      const response = await httpClient.post<TaskBasicFormDataUpdateOrDelete>('/api/v1/task/exec/update', data);

      console.log('更新复合表单响应:', response);
      return response;
    } catch (error) {
      console.error('更新复合表单失败:', error);
      throw error;
    }
  }

  // ==================== 告警相关方法 ====================

  /**
   * 获取告警列表
   * @returns 告警列表
   */
  static async getAlerts(params: Partial<TaskAlertSearchParams>): Promise<ApiResponse<TaskAlert>> {
    const res = await httpClient.post<ApiResponse<TaskAlert>>('/api/v1/task/alert/select', params);
    return res;
  }

  /**
   * 删除单个告警
   * @param id 告警ID
   * @returns 删除结果
   */
  static async deleteAlert(id: number): Promise<ApiResponse<TaskAlert>> {
    console.log('删除告警:', id);

    // 请求后端参数包装成map
    const params = {
      ids: [id],
    };

    const response = await httpClient.post<ApiResponse<TaskAlert>>('/api/v1/task/alert/delete/id', params);

    return response;
  }

  /**
   * 批量删除告警
   * @param ids 告警ID数组
   * @returns 删除结果
   */
  static async batchDeleteAlerts(ids: number[]): Promise<ApiResponse<TaskAlert>> {
    console.log('批量删除告警:', ids);

    // 请求后端参数包装成map
    const params = {
      ids: ids,
    };

    const response = await httpClient.post<ApiResponse<TaskAlert>>('/api/v1/task/alert/delete/id', params);
    return response;
  }

  /**
   * 添加告警
   * @param data 告警数据
   * @returns 新创建的告警信息
   */
  static async addAlert(data: TaskAlert): Promise<ApiResponse<TaskAlert>> {
    console.log('添加告警:', data);
    const response = await httpClient.post<ApiResponse<TaskAlert>>('/api/v1/task/alert/add', data);
    console.log('添加告警响应:', response);
    return response;
  }

  /**
   * 添加告警
   * @param data 告警数据
   * @returns 新创建的告警信息
   */
  static async updateAlert(data: TaskAlert): Promise<ApiResponse<TaskAlert>> {
    console.log('添加告警:', data);
    const response = await httpClient.post<ApiResponse<TaskAlert>>('/api/v1/task/alert/update', data);
    console.log('添加告警响应:', response);
    return response;
  }

  // ==================== 数据库连接相关方法 ====================

  /**
   * 获取所有数据库连接列表
   * @returns 数据库连接列表
   */
  static async getDbConnections(params: Partial<DBConnectionSearchParams>): Promise<ApiResponse<DBConnection>> {
    const res = httpClient.post<ApiResponse<DBConnection>>('/api/v1/db/connection/select', params);
    return res;
  }

  /**
   * 根据数字ID获取单个数据库连接
   * @param id 数据库连接ID
   * @returns 数据库连接信息
   */
  static async getDbConnectionByIdNumber(id: number): Promise<DBConnection | null> {
    // 模拟根据ID查询
    const connection = mockDbConnectionData.find(conn => conn.id === id);
    return connection || null;
  }

  /**
   * 根据字符串ID获取数据库连接
   * @param id 数据库连接ID
   * @returns 数据库连接信息
   */
  static async getDbConnectionById(id: string): Promise<DBConnection | null> {
    // 模拟根据ID查询
    const connection = mockDbConnectionData.find(conn => `db_${conn.id}` === id);
    return connection || null;
  }

  // ==================== 告警发送相关方法 ====================

  /**
   * 获取告警发送列表
   * @returns 告警发送列表
   */
  static async getAlertSends(params: Partial<AlertSendSearchParams>): Promise<ApiResponse<AlertSend>> {
    const res = await httpClient.post<ApiResponse<AlertSend>>('/api/v1/alert/send/select', params);
    console.log('API响应数据:', res);

    res.data.map(item => {
      // console.log('处理项目:', item);
      // console.log('config类型:', typeof item.config);
      // console.log('config内容:', item.config);

      if (item.receive_type == 'kafka') {
        // 如果 config 是字符串，则解析为对象
        if (typeof item.config === 'string') {
          try {
            item.config = JSON.parse(item.config) as KafkaReceiver;
            // console.log('Kafka配置解析成功:', item.config);
          } catch (error) {
            console.error('Kafka配置JSON解析失败:', error);
            console.error('原始config字符串:', item.config);
            // 设置默认值避免程序崩溃
            item.config = {
              address: '',
              username: '',
              password: '',
              topic: '',
            } as KafkaReceiver;
          }
        }
      } else {
        // 如果 config 是字符串，则解析为对象
        if (typeof item.config === 'string') {
          try {
            item.config = JSON.parse(item.config) as PrometheusReceiver;
            // console.log('Prometheus配置解析成功:', item.config);
          } catch (error) {
            console.error('Prometheus配置JSON解析失败:', error);
            console.error('原始config字符串:', item.config);
            // 设置默认值避免程序崩溃
            item.config = {
              address: '',
              username: '',
              password: '',
            } as PrometheusReceiver;
          }
        }
      }
    });

    return res;
  }

  // ==================== 其他信息相关方法 ====================

  /**
   * 获取其他信息列表
   * @returns 其他信息列表
   */
  static async getOtherInfos(): Promise<OtherInfo[]> {
    return mockOtherInfoData;
  }

  /**
   * 根据ID获取其他信息
   * @param id 其他信息ID
   * @returns 其他信息
   */
  static async getOtherInfoById(id: string): Promise<OtherInfo | null> {
    // 模拟根据ID查询
    const info = mockOtherInfoData.find(info => `info_${info.id}` === id);
    return info || null;
  }

  // ==================== 数据库连接 CRUD 方法 ====================

  // ==================== 告警发送 CRUD 方法 ====================

  // ==================== 其他信息 CRUD 方法 ====================
}
