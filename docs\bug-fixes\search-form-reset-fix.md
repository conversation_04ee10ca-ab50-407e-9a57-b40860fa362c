# 快速搜索表单重置按钮修复

## 问题描述

在快速搜索表单区域，重置按钮第一次按下时数据不对。具体表现为：

1. 用户在搜索表单中输入搜索条件并执行搜索
2. 点击重置按钮
3. 第一次点击重置按钮时，表单字段被清空，但数据加载时仍然使用了之前的搜索条件
4. 需要再次点击重置按钮才能真正重置数据

## 问题原因

这是一个典型的 React 状态更新时序问题：

1. **异步状态更新**：React 的状态更新是异步的，当调用 `resetData()` 和 `updatePagination()` 时，状态可能还没有立即更新
2. **依赖旧状态**：在 `loadData` 函数中，参数合并逻辑是：
   ```typescript
   const finalParams = {
     ...searchParams,  // 可能还是旧的搜索参数
     ...pagination,    // 可能还是旧的分页参数
     ...customParams,  // 传入的自定义参数
   };
   ```
3. **参数优先级**：虽然 `customParams` 具有最高优先级，但如果没有明确传递搜索字段，旧的 `searchParams` 仍然会被使用

## 解决方案

### 修复方法

在重置函数中，明确传递所有搜索参数为 `undefined`，确保覆盖可能还未更新的状态值。

### 修复前的代码示例

```typescript
const handleReset = useCallback(() => {
  searchForm.resetFields();
  resetSortAndFilter();
  resetData();
  updatePagination(DEFAULT_PAGINATION.current, DEFAULT_PAGINATION.page_size);
  loadDataRef.current({
    current: DEFAULT_PAGINATION.current,
    page_size: DEFAULT_PAGINATION.page_size,
  });
}, [searchForm, resetSortAndFilter, resetData, updatePagination]);
```

### 修复后的代码示例

```typescript
const handleReset = useCallback(() => {
  // 重置所有状态
  searchForm.resetFields();
  resetSortAndFilter();
  resetData();
  clearSelection();

  // 重新加载数据，明确传递空的搜索参数以确保重置生效
  loadDataRef.current({
    current: DEFAULT_PAGINATION.current,
    page_size: DEFAULT_PAGINATION.page_size,
    // 明确传递空的搜索参数，覆盖可能还未更新的状态
    name: undefined,
    db_type: undefined,
    host: undefined,
  });
}, [searchForm, resetSortAndFilter, resetData, clearSelection]);
```

## 修复范围

本次修复涉及以下文件：

1. **DBConnectionTable.tsx** - 数据库连接表格
   - 搜索字段：`name`, `db_type`, `host`

2. **OtherInfoTable.tsx** - 其他信息表格
   - 搜索字段：`name`, `business`, `hostname`

3. **AlertSendTable.tsx** - 告警发送配置表格
   - 搜索字段：`name`, `type`

4. **MainTable.tsx** - 主表格
   - 搜索字段：`name`, `group_name`, `status`, `weekday`, `frequency`, `start_time`, `end_time`, `retry_num`, `retry_frequency`, `alert_receiver`, `group`

5. **AlertTable.tsx** - 告警配置表格（已修复）
   - 搜索字段：`name`, `severity`, `type`, `sql`

## 修复原理

1. **参数优先级**：在 `loadData` 函数中，`customParams` 具有最高优先级，会覆盖 `searchParams` 中的同名字段
2. **明确重置**：通过明确传递 `undefined` 值，确保所有搜索字段都被重置
3. **状态一致性**：避免依赖异步状态更新，确保重置行为的一致性

## 测试验证

### 测试步骤

1. 打开任意包含搜索表单的页面（如数据库连接管理）
2. 在搜索表单中输入搜索条件（如连接名称、主机地址等）
3. 点击"搜索"按钮，确认数据被过滤
4. 点击"重置"按钮
5. 验证：
   - 表单字段被清空
   - 数据立即恢复到初始状态（显示所有数据）
   - 分页重置到第一页

### 预期结果

- 第一次点击重置按钮就能完全重置搜索条件和数据
- 不需要多次点击重置按钮
- 重置后的数据加载使用默认参数（无搜索条件）

## 相关文档

- [告警表格重置修复](./alert-table-reset-fix.md) - 类似问题的修复记录
- [分页修复总结](../PAGINATION_FIX_SUMMARY.md) - 分页相关问题修复
